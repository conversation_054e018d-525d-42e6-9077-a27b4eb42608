# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=debug
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30

# MySQL数据库配置
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=t_solve_go_api
MYSQL_CHARSET=utf8mb4
DB_MAX_OPEN_CONNS=100
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME=3600

# Redis配置
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_PASSWORD=Suyan15913..
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5
REDIS_DIAL_TIMEOUT=5
REDIS_READ_TIMEOUT=3
REDIS_WRITE_TIMEOUT=3
REDIS_IDLE_TIMEOUT=300

# Qwen API配置
QWEN_KEY=sk-21d8cdf3309e462cbf9391a4963862f0
QWEN_BASE_URL=https://dashscope.aliyuncs.com
QWEN_TIMEOUT=60
QWEN_MAX_RETRIES=3

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILENAME=logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=5
LOG_MAX_AGE=30
LOG_COMPRESS=true
