package utils

import (
	"crypto/md5"
	"fmt"
	"regexp"
	"strings"
	"time"

	"qwen-solve/internal/model"
)

// GenerateCacheKey 生成缓存键
// 根据题目类型、内容和选项生成唯一的缓存键
func GenerateCacheKey(questType model.QuestionType, questContent string, questOptions map[string]string) (hashKey, hashRaw string) {
	// 1. 拼接字符串
	var parts []string
	parts = append(parts, string(questType))
	parts = append(parts, questContent)

	// 根据题目类型添加选项
	switch questType {
	case model.QuestionTypeJudge:
		// 判断题只有两个选项
		if val, ok := questOptions["Y"]; ok {
			parts = append(parts, val)
		}
		if val, ok := questOptions["N"]; ok {
			parts = append(parts, val)
		}
		// 兼容A/B格式的判断题
		if val, ok := questOptions["A"]; ok {
			parts = append(parts, val)
		}
		if val, ok := questOptions["B"]; ok {
			parts = append(parts, val)
		}
	case model.QuestionTypeSingle, model.QuestionTypeMultiple:
		// 单选题和多选题有四个选项，按字母顺序添加
		options := []string{"A", "B", "C", "D"}
		for _, opt := range options {
			if val, ok := questOptions[opt]; ok {
				parts = append(parts, val)
			}
		}
	}

	// 2. 拼接成原始字符串
	rawText := strings.Join(parts, "")

	// 3. 使用正则清洗空格、换行符和标点符号
	cleanText := CleanTextForHash(rawText)

	// 4. hashRaw 存储清洗后的文本（用于调试和审计）
	hashRaw = cleanText

	// 5. 生成MD5哈希值
	hashKey = GenerateMD5(cleanText)

	return hashKey, hashRaw
}

// CleanTextForHash 清洗文本用于哈希生成
// 使用正则 [\s\p{P}\x{3000}]+ 清洗空格换行符以及标点符号
func CleanTextForHash(text string) string {
	// 正则表达式说明：
	// \s - 匹配任何空白字符（空格、制表符、换行符等）
	// \p{P} - 匹配任何标点符号
	// \x{3000} - 匹配中文全角空格
	re := regexp.MustCompile(`[\s\p{P}\x{3000}]+`)

	// 替换为空字符串
	cleaned := re.ReplaceAllString(text, "")

	// 转换为小写以确保一致性
	cleaned = strings.ToLower(cleaned)

	return cleaned
}

// GenerateMD5 生成MD5哈希
func GenerateMD5(text string) string {
	hash := md5.Sum([]byte(text))
	return fmt.Sprintf("%x", hash)
}



// GenerateRequestID 生成请求ID
func GenerateRequestID() string {
	// 使用当前时间戳 + 随机数
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("req_%d", timestamp)
}

// ValidateQuestionOptions 验证题目选项数量
func ValidateQuestionOptions(questType model.QuestionType, options map[string]string) error {
	switch questType {
	case model.QuestionTypeJudge:
		// 判断题必须有2个选项
		if len(options) != 2 {
			return fmt.Errorf("判断题必须有2个选项，当前有%d个", len(options))
		}
	case model.QuestionTypeSingle, model.QuestionTypeMultiple:
		// 单选题和多选题必须有4个选项
		if len(options) != 4 {
			return fmt.Errorf("单选题/多选题必须有4个选项，当前有%d个", len(options))
		}
		// 检查是否包含A、B、C、D选项
		requiredOptions := []string{"A", "B", "C", "D"}
		for _, opt := range requiredOptions {
			if _, exists := options[opt]; !exists {
				return fmt.Errorf("缺少选项%s", opt)
			}
		}
	default:
		return fmt.Errorf("无效的题目类型: %s", questType)
	}

	return nil
}


