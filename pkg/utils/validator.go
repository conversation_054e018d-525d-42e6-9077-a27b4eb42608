package utils

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

// ValidateImageURL 验证图片URL是否可访问
func ValidateImageURL(imageURL string) error {
	// 1. 验证URL格式
	if imageURL == "" {
		return fmt.Errorf("图片URL不能为空")
	}

	// 解析URL
	parsedURL, err := url.Parse(imageURL)
	if err != nil {
		return fmt.Errorf("图片URL格式不正确: %v", err)
	}

	// 检查协议
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("图片URL必须使用http或https协议")
	}

	// 2. 检查图片扩展名（可选）
	if !isImageExtension(parsedURL.Path) {
		// 不强制要求扩展名，因为有些图片URL可能没有扩展名
		// return fmt.Errorf("URL不是有效的图片格式")
	}

	// 3. 发送HEAD请求检查图片是否可访问
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("HEAD", imageURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; QwenSolveBot/1.0)")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("图片不存在,上传失败,请联系管理员处理！")
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("图片不存在,上传失败,请联系管理员处理！")
	}

	// 检查Content-Type（可选）
	contentType := resp.Header.Get("Content-Type")
	if contentType != "" && !isImageContentType(contentType) {
		// 不强制要求Content-Type，因为有些服务器可能不返回正确的类型
		// return fmt.Errorf("URL内容不是图片格式")
	}

	return nil
}

// isImageExtension 检查是否为图片扩展名
func isImageExtension(path string) bool {
	imageExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	lowerPath := strings.ToLower(path)
	
	for _, ext := range imageExtensions {
		if strings.HasSuffix(lowerPath, ext) {
			return true
		}
	}
	
	return false
}

// isImageContentType 检查是否为图片Content-Type
func isImageContentType(contentType string) bool {
	imageTypes := []string{
		"image/jpeg", "image/jpg", "image/png", "image/gif",
		"image/bmp", "image/webp", "image/svg+xml",
	}
	
	lowerType := strings.ToLower(contentType)
	for _, imgType := range imageTypes {
		if strings.Contains(lowerType, imgType) {
			return true
		}
	}
	
	return false
}

// ValidateQuestionContent 验证题干内容是否干净
func ValidateQuestionContent(content string) error {
	if content == "" {
		return fmt.Errorf("题干内容不能为空")
	}

	// 检查长度
	if len(content) < 5 {
		return fmt.Errorf("图片不标准，题干未正确识别，请重新拍摄！")
	}

	// 检查是否包含基本的中文字符
	hasChineseChar := regexp.MustCompile(`[\p{Han}]`).MatchString(content)
	if !hasChineseChar {
		return fmt.Errorf("图片不标准，题干未正确识别，请重新拍摄！")
	}

	// 检查是否包含过多的特殊字符或乱码
	specialCharRatio := getSpecialCharRatio(content)
	if specialCharRatio > 0.3 { // 如果特殊字符超过30%，认为识别不准确
		return fmt.Errorf("图片不标准，题干未正确识别，请重新拍摄！")
	}

	return nil
}

// getSpecialCharRatio 计算特殊字符比例
func getSpecialCharRatio(text string) float64 {
	if len(text) == 0 {
		return 0
	}

	specialCharCount := 0
	totalChars := len([]rune(text))

	for _, char := range text {
		// 如果不是中文、英文、数字、常见标点符号，则认为是特殊字符
		if !regexp.MustCompile(`[\p{Han}\p{L}\p{N}，。？！、；：""''（）【】《》\s]`).MatchString(string(char)) {
			specialCharCount++
		}
	}

	return float64(specialCharCount) / float64(totalChars)
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidatePhone 验证手机号格式
func ValidatePhone(phone string) bool {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}

// ValidateIP 验证IP地址格式
func ValidateIP(ip string) bool {
	ipRegex := regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
	return ipRegex.MatchString(ip)
}

// SanitizeString 清理字符串，移除危险字符
func SanitizeString(input string) string {
	// 移除HTML标签
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	cleaned := htmlRegex.ReplaceAllString(input, "")
	
	// 移除SQL注入相关字符
	sqlRegex := regexp.MustCompile(`[';\"\\]`)
	cleaned = sqlRegex.ReplaceAllString(cleaned, "")
	
	// 移除脚本相关字符
	scriptRegex := regexp.MustCompile(`[<>{}]`)
	cleaned = scriptRegex.ReplaceAllString(cleaned, "")
	
	return strings.TrimSpace(cleaned)
}

// TruncateString 截断字符串到指定长度
func TruncateString(str string, maxLen int) string {
	if len(str) <= maxLen {
		return str
	}
	
	runes := []rune(str)
	if len(runes) <= maxLen {
		return str
	}
	
	return string(runes[:maxLen]) + "..."
}

// IsValidJSON 检查字符串是否为有效的JSON
func IsValidJSON(str string) bool {
	str = strings.TrimSpace(str)
	return (strings.HasPrefix(str, "{") && strings.HasSuffix(str, "}")) ||
		   (strings.HasPrefix(str, "[") && strings.HasSuffix(str, "]"))
}
