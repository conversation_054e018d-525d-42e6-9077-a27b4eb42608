package utils

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"qwen-solve/internal/model"
)

// SuccessResponse 成功响应
func SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, model.APIResponse{
		Code:    model.CodeSuccess,
		Message: model.ErrorMessages[model.CodeSuccess],
		Data:    data,
	})
}

// ErrorResponse 错误响应
func ErrorResponse(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	
	if message == "" {
		if msg, exists := model.ErrorMessages[code]; exists {
			message = msg
		} else {
			message = "未知错误"
		}
	}

	c.JSO<PERSON>(httpStatus, model.APIResponse{
		Code:    code,
		Message: message,
	})
}

// ErrorResponseWithDetails 带详细信息的错误响应
func ErrorResponseWithDetails(c *gin.Context, code int, message, details string) {
	httpStatus := getHTTPStatus(code)
	
	if message == "" {
		if msg, exists := model.ErrorMessages[code]; exists {
			message = msg
		} else {
			message = "未知错误"
		}
	}

	response := model.APIResponse{
		Code:    code,
		Message: message,
	}

	if details != "" {
		response.Data = map[string]string{"details": details}
	}

	c.JSON(httpStatus, response)
}

// ValidationErrorResponse 验证错误响应
func ValidationErrorResponse(c *gin.Context, err error) {
	ErrorResponseWithDetails(c, model.CodeValidationFail, 
		model.ErrorMessages[model.CodeValidationFail], err.Error())
}

// InternalErrorResponse 内部错误响应
func InternalErrorResponse(c *gin.Context, err error) {
	message := model.ErrorMessages[model.CodeInternalError]
	details := ""
	
	if err != nil {
		details = err.Error()
	}
	
	ErrorResponseWithDetails(c, model.CodeInternalError, message, details)
}

// getHTTPStatus 根据业务错误码获取HTTP状态码
func getHTTPStatus(code int) int {
	switch {
	case code == model.CodeSuccess:
		return http.StatusOK
	case code >= 400 && code < 500:
		return code
	case code >= 500 && code < 600:
		return code
	case code >= 1000 && code < 2000:
		return http.StatusBadRequest
	default:
		return http.StatusInternalServerError
	}
}

// PaginationResponse 分页响应
func PaginationResponse(c *gin.Context, data interface{}, total int64, page, pageSize int) {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	response := map[string]interface{}{
		"list":        data,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	}
	
	SuccessResponse(c, response)
}

// GetClientIP 获取客户端IP地址
func GetClientIP(c *gin.Context) string {
	// 尝试从X-Forwarded-For头获取
	if ip := c.GetHeader("X-Forwarded-For"); ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		if idx := len(ip); idx > 0 {
			if commaIdx := 0; commaIdx < idx {
				for i, char := range ip {
					if char == ',' {
						commaIdx = i
						break
					}
				}
				if commaIdx > 0 {
					return ip[:commaIdx]
				}
			}
			return ip
		}
	}
	
	// 尝试从X-Real-IP头获取
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}
	
	// 使用RemoteAddr
	return c.ClientIP()
}

// GetUserAgent 获取用户代理
func GetUserAgent(c *gin.Context) string {
	return c.GetHeader("User-Agent")
}

// GetCurrentTimeMillis 获取当前时间戳（毫秒）
func GetCurrentTimeMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// FormatDuration 格式化持续时间
func FormatDuration(milliseconds int64) string {
	duration := time.Duration(milliseconds) * time.Millisecond
	
	if duration < time.Second {
		return duration.String()
	}
	
	seconds := duration.Seconds()
	if seconds < 60 {
		return duration.Truncate(time.Millisecond).String()
	}
	
	return duration.Truncate(time.Second).String()
}

// SetCacheHeaders 设置缓存头
func SetCacheHeaders(c *gin.Context, maxAge int) {
	c.Header("Cache-Control", "public, max-age="+string(rune(maxAge)))
	c.Header("Expires", time.Now().Add(time.Duration(maxAge)*time.Second).Format(http.TimeFormat))
}

// SetNoCacheHeaders 设置不缓存头
func SetNoCacheHeaders(c *gin.Context) {
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
}

// CORS 跨域处理
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.Header("Access-Control-Max-Age", "86400")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// Recovery 错误恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		InternalErrorResponse(c, fmt.Errorf("panic recovered: %v", recovered))
		c.Abort()
	})
}
