package database

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"qwen-solve/internal/config"
)

// DB 数据库连接实例
var DB *sql.DB

// InitMySQL 初始化MySQL连接
func InitMySQL(cfg *config.DatabaseConfig) error {
	var err error
	
	// 构建DSN
	dsn := cfg.GetDSN()
	
	// 打开数据库连接
	DB, err = sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	DB.SetMaxOpenConns(cfg.MaxOpenConns)
	DB.SetMaxIdleConns(cfg.MaxIdleConns)
	DB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// 测试连接
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	return nil
}

// CloseMySQL 关闭MySQL连接
func CloseMySQL() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// GetDB 获取数据库连接
func GetDB() *sql.DB {
	return DB
}

// Transaction 事务处理函数
func Transaction(fn func(*sql.Tx) error) error {
	tx, err := DB.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}

// Paginate 分页查询辅助函数
func Paginate(page, pageSize int) (offset int, limit int) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}
	
	offset = (page - 1) * pageSize
	limit = pageSize
	return
}

// BuildWhereClause 构建WHERE子句
func BuildWhereClause(conditions map[string]interface{}) (string, []interface{}) {
	if len(conditions) == 0 {
		return "", nil
	}

	var whereParts []string
	var args []interface{}

	for field, value := range conditions {
		if value == nil {
			continue
		}
		
		switch v := value.(type) {
		case string:
			if v != "" {
				whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
				args = append(args, v)
			}
		case *string:
			if v != nil && *v != "" {
				whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
				args = append(args, *v)
			}
		case bool:
			whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
			args = append(args, v)
		case *bool:
			if v != nil {
				whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
				args = append(args, *v)
			}
		case int, int64, uint64:
			whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
			args = append(args, v)
		default:
			whereParts = append(whereParts, fmt.Sprintf("%s = ?", field))
			args = append(args, v)
		}
	}

	if len(whereParts) == 0 {
		return "", nil
	}

	whereClause := "WHERE " + whereParts[0]
	for i := 1; i < len(whereParts); i++ {
		whereClause += " AND " + whereParts[i]
	}

	return whereClause, args
}

// BuildLikeClause 构建LIKE查询子句
func BuildLikeClause(field, keyword string) (string, interface{}) {
	if keyword == "" {
		return "", nil
	}
	return fmt.Sprintf("%s LIKE ?", field), "%" + keyword + "%"
}

// BuildDateRangeClause 构建日期范围查询子句
func BuildDateRangeClause(field, startDate, endDate string) (string, []interface{}) {
	var parts []string
	var args []interface{}

	if startDate != "" {
		if _, err := time.Parse("2006-01-02", startDate); err == nil {
			parts = append(parts, fmt.Sprintf("%s >= ?", field))
			args = append(args, startDate+" 00:00:00")
		}
	}

	if endDate != "" {
		if _, err := time.Parse("2006-01-02", endDate); err == nil {
			parts = append(parts, fmt.Sprintf("%s <= ?", field))
			args = append(args, endDate+" 23:59:59")
		}
	}

	if len(parts) == 0 {
		return "", nil
	}

	clause := parts[0]
	for i := 1; i < len(parts); i++ {
		clause += " AND " + parts[i]
	}

	return clause, args
}

// IsNoRowsError 检查是否为无记录错误
func IsNoRowsError(err error) bool {
	return err == sql.ErrNoRows
}
