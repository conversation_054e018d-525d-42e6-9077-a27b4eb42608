package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"qwen-solve/internal/config"
)

// Client Redis客户端实例
var Client *redis.Client

// InitRedis 初始化Redis连接
func InitRedis(cfg *config.RedisConfig) error {
	Client = redis.NewClient(&redis.Options{
		Addr:         cfg.GetRedisAddr(),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,
		ConnMaxIdleTime: cfg.IdleTimeout,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := Client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to ping redis: %w", err)
	}

	return nil
}

// CloseRedis 关闭Redis连接
func CloseRedis() error {
	if Client != nil {
		return Client.Close()
	}
	return nil
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return Client
}

// Set 设置键值对
func Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return Client.Set(ctx, key, value, expiration).Err()
}

// SetJSON 设置JSON数据
func SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal json: %w", err)
	}
	return Client.Set(ctx, key, data, expiration).Err()
}

// Get 获取字符串值
func Get(ctx context.Context, key string) (string, error) {
	result := Client.Get(ctx, key)
	if err := result.Err(); err != nil {
		if err == redis.Nil {
			return "", nil // 键不存在
		}
		return "", err
	}
	return result.Val(), nil
}

// GetJSON 获取JSON数据
func GetJSON(ctx context.Context, key string, dest interface{}) error {
	result := Client.Get(ctx, key)
	if err := result.Err(); err != nil {
		if err == redis.Nil {
			return nil // 键不存在
		}
		return err
	}

	data := result.Val()
	if data == "" {
		return nil
	}

	return json.Unmarshal([]byte(data), dest)
}

// Exists 检查键是否存在
func Exists(ctx context.Context, key string) (bool, error) {
	result := Client.Exists(ctx, key)
	if err := result.Err(); err != nil {
		return false, err
	}
	return result.Val() > 0, nil
}

// Delete 删除键
func Delete(ctx context.Context, keys ...string) error {
	return Client.Del(ctx, keys...).Err()
}

// Expire 设置键的过期时间
func Expire(ctx context.Context, key string, expiration time.Duration) error {
	return Client.Expire(ctx, key, expiration).Err()
}

// TTL 获取键的剩余生存时间
func TTL(ctx context.Context, key string) (time.Duration, error) {
	result := Client.TTL(ctx, key)
	if err := result.Err(); err != nil {
		return 0, err
	}
	return result.Val(), nil
}

// Keys 获取匹配模式的所有键
func Keys(ctx context.Context, pattern string) ([]string, error) {
	result := Client.Keys(ctx, pattern)
	if err := result.Err(); err != nil {
		return nil, err
	}
	return result.Val(), nil
}

// FlushDB 清空当前数据库
func FlushDB(ctx context.Context) error {
	return Client.FlushDB(ctx).Err()
}

// Info 获取Redis信息
func Info(ctx context.Context, section ...string) (string, error) {
	result := Client.Info(ctx, section...)
	if err := result.Err(); err != nil {
		return "", err
	}
	return result.Val(), nil
}

// Pipeline 管道操作
func Pipeline(ctx context.Context, fn func(redis.Pipeliner) error) error {
	pipe := Client.Pipeline()
	if err := fn(pipe); err != nil {
		return err
	}
	_, err := pipe.Exec(ctx)
	return err
}

// Transaction 事务操作
func Transaction(ctx context.Context, keys []string, fn func(*redis.Tx) error) error {
	return Client.Watch(ctx, func(tx *redis.Tx) error {
		return fn(tx)
	}, keys...)
}

// HSet 设置哈希字段
func HSet(ctx context.Context, key string, values ...interface{}) error {
	return Client.HSet(ctx, key, values...).Err()
}

// HGet 获取哈希字段值
func HGet(ctx context.Context, key, field string) (string, error) {
	result := Client.HGet(ctx, key, field)
	if err := result.Err(); err != nil {
		if err == redis.Nil {
			return "", nil
		}
		return "", err
	}
	return result.Val(), nil
}

// HGetAll 获取哈希所有字段
func HGetAll(ctx context.Context, key string) (map[string]string, error) {
	result := Client.HGetAll(ctx, key)
	if err := result.Err(); err != nil {
		return nil, err
	}
	return result.Val(), nil
}

// HDel 删除哈希字段
func HDel(ctx context.Context, key string, fields ...string) error {
	return Client.HDel(ctx, key, fields...).Err()
}

// Incr 递增
func Incr(ctx context.Context, key string) (int64, error) {
	result := Client.Incr(ctx, key)
	if err := result.Err(); err != nil {
		return 0, err
	}
	return result.Val(), nil
}

// Decr 递减
func Decr(ctx context.Context, key string) (int64, error) {
	result := Client.Decr(ctx, key)
	if err := result.Err(); err != nil {
		return 0, err
	}
	return result.Val(), nil
}
