# Qwen Solve API

基于Go语言开发的智能图片题目识别和解答API服务，使用Qwen AI模型进行图片识别和答案生成。

## 功能特性

- 🖼️ **图片题目识别** - 支持单选题、多选题、判断题的图片识别
- 🤖 **AI智能解答** - 使用Qwen-VL-Plus和Qwen-Plus模型生成权威答案
- 🚀 **多级缓存** - Redis + MySQL双重缓存，提升响应速度
- 📊 **完整日志** - 详细的请求日志和统计信息
- 🛠️ **题库管理** - 支持题库的增删改查操作
- 🔧 **高可用性** - 规范化架构设计，易于扩展和维护

## 技术栈

- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **AI模型**: Qwen-VL-Plus, Qwen-Plus
- **语言**: Go 1.21+

## 项目结构

```
qwen-solve/
├── cmd/server/           # 应用程序入口
├── internal/            # 内部包
│   ├── config/         # 配置管理
│   ├── handler/        # HTTP处理器
│   ├── service/        # 业务逻辑层
│   ├── repository/     # 数据访问层
│   ├── model/          # 数据模型
│   ├── middleware/     # 中间件
│   └── context/        # 上下文管理
├── pkg/                # 公共包
│   ├── database/       # 数据库连接
│   ├── redis/          # Redis客户端
│   └── utils/          # 工具函数
├── configs/            # 配置文件
├── docs/               # 文档
└── scripts/            # 脚本文件
```

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd qwen-solve
```

2. **安装依赖**
```bash
make deps
```

3. **配置环境变量**
```bash
make init-env
# 编辑 .env 文件，配置数据库和Redis连接信息
```

4. **初始化数据库**
```bash
make init-db
```

5. **运行项目**
```bash
make run
```

### 开发模式

```bash
# 安装开发工具
make install-tools

# 热重载开发
make dev
```

## API接口

### 核心接口

#### 图片解题
```http
POST /api/v1/solve
Content-Type: application/json

{
  "image_url": "http://example.com/image.jpg"
}
```

#### 验证图片
```http
POST /api/v1/solve/validate
Content-Type: application/json

{
  "image_url": "http://example.com/image.jpg"
}
```

### 管理接口

#### 获取请求日志
```http
GET /api/v1/logs?page=1&page_size=20
```

#### 题库管理
```http
GET /api/v1/questions?page=1&page_size=20
POST /api/v1/questions
PUT /api/v1/questions/{id}
DELETE /api/v1/questions/{id}
```

## 业务流程

1. **图片验证** - 验证用户提交的图片URL是否可访问
2. **图片识别** - 调用Qwen-VL-Plus模型识别图片中的题目
3. **数据解析** - 解析AI返回的数据，提取题型、题干、选项
4. **缓存查询** - 生成缓存键，先查Redis，再查MySQL
5. **答案生成** - 如果缓存未命中，调用Qwen-Plus模型生成答案
6. **数据存储** - 将结果存入MySQL并回写Redis
7. **日志记录** - 记录完整的请求日志

## 数据库设计

### 核心表结构

- `qwen_solve_questions` - 题库表
- `qwen_solve_request_logs` - 请求日志表
- `qwen_solve_system_configs` - 系统配置表
- `qwen_solve_api_statistics` - API统计表

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `SERVER_PORT` | 服务端口 | 8080 |
| `MYSQL_HOST` | MySQL主机 | localhost |
| `MYSQL_PORT` | MySQL端口 | 3306 |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `QWEN_KEY` | Qwen API密钥 | - |

## 部署

### 构建

```bash
# 构建当前平台
make build

# 构建所有平台
make build-all
```

### Docker部署

```bash
# 构建镜像
make docker-build

# 运行容器
make docker-run
```

## 开发指南

### 代码规范

```bash
# 格式化代码
make fmt

# 代码检查
make vet

# 质量检查
make lint
```

### 测试

```bash
# 运行测试
make test

# 生成覆盖率报告
make test-coverage
```

## 监控和日志

### 健康检查
```http
GET /health
```

### 日志级别
- `debug` - 调试信息
- `info` - 一般信息
- `warn` - 警告信息
- `error` - 错误信息

## 性能优化

- **连接池管理** - 数据库和Redis连接池优化
- **缓存策略** - 多级缓存提升响应速度
- **并发控制** - 合理的并发处理机制
- **资源释放** - 及时释放系统资源

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证连接参数是否正确

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证密码和端口配置

3. **Qwen API调用失败**
   - 检查API密钥是否有效
   - 验证网络连接是否正常

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: [<EMAIL>]
- 项目地址: [repository-url]
