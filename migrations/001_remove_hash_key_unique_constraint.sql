-- 迁移脚本：移除 hash_key 字段的唯一性约束
-- 版本：001
-- 日期：2025-01-14
-- 描述：允许相同的缓存键存入数据库，支持题库管理业务需求

-- 检查当前约束是否存在
SELECT CONSTRAINT_NAME 
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'qwen_solve_questions' 
  AND CONSTRAINT_NAME = 'uk_hash_key';

-- 如果约束存在，则删除唯一性约束
SET @sql = (
    SELECT IF(
        EXISTS(
            SELECT 1 
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
            WHERE TABLE_SCHEMA = DATABASE() 
              AND TABLE_NAME = 'qwen_solve_questions' 
              AND CONSTRAINT_NAME = 'uk_hash_key'
        ),
        'ALTER TABLE qwen_solve_questions DROP INDEX uk_hash_key;',
        'SELECT "约束 uk_hash_key 不存在，无需删除" as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加普通索引（如果不存在）
SET @sql = (
    SELECT IF(
        NOT EXISTS(
            SELECT 1 
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
              AND TABLE_NAME = 'qwen_solve_questions' 
              AND INDEX_NAME = 'idx_hash_key'
        ),
        'ALTER TABLE qwen_solve_questions ADD INDEX idx_hash_key (hash_key);',
        'SELECT "索引 idx_hash_key 已存在，无需添加" as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证修改结果
SELECT 
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'qwen_solve_questions'
ORDER BY CONSTRAINT_TYPE, CONSTRAINT_NAME;

SELECT 
    INDEX_NAME,
    NON_UNIQUE,
    COLUMN_NAME
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'qwen_solve_questions'
  AND INDEX_NAME IN ('uk_hash_key', 'idx_hash_key')
ORDER BY INDEX_NAME;

-- 输出完成信息
SELECT 'hash_key 唯一性约束已成功移除，允许重复缓存键存入' as migration_status;
