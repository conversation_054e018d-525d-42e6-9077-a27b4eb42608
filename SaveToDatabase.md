SaveToDatabase方法业务逻辑介绍：

预期中qwen-plus返回的数据中样本如下

{
	"code": 0,
	"message": "成功",
	"data": "{\"output\":{\"choices\":[{\"message\":{\"content\":\"{\\n  \\\"answer\\\": {\\n    \\\"A\\\": \\\"越二级挡位减挡\\\",\\n    \\\"B\\\": \\\"握稳转向盘\\\",\\n    \\\"C\\\": \\\"利用障碍刷蹭停车\\\",\\n    \\\"D\\\": \\\"开启车辆缓速器\\\"\\n  },\\n  \\\"analysis\\\": \\\"在大型客货车制动失效、车辆失控的情况下，正确的做法包括：1) 握稳转向盘以保持方向稳定（选项B）；2) 利用发动机牵阻作用进行越级减挡来降低车速（选项A）；3) 开启车辆缓速器辅助减速（选项D）；4) 在紧急情况下可利用路边障碍物蹭停车辆（选项C）。以上措施均有助于控制车辆并减少事故风险。\\\"\\n}\",\"role\":\"assistant\"},\"finish_reason\":\"stop\"}]},\"usage\":{\"total_tokens\":254,\"output_tokens\":156,\"input_tokens\":98,\"prompt_tokens_details\":{\"cached_tokens\":0}},\"request_id\":\"e71104c8-5a50-9913-bab9-4c6bb32fa9d0\"}",
	"trace_id": "daln5o5nznoo"
}
解析后获得以下内容
    {
	"answer": [
		{
			"A": "越二级挡位减挡"
		},
		{
			"B": "握稳转向盘"
		},
		{
			"C": "利用障碍刷蹭停车"
		},
		{
			"D": "开启车辆缓速器"
		}
	],
    "analysis": "在大型客货车制动失效、车辆失控的情况下，正确的做法包括：1) 握稳转向盘以保持方向稳定（选项B）；2) 利用发动机牵阻作用进行越级减挡来降低车速（选项A）；3) 开启车辆缓速器辅助减速（选项D）；4) 在紧急情况下可利用路边障碍物蹭停车辆（选项C）。以上措施均有助于控制车辆并减少事故风险。},


{
	"code": 0,
	"message": "成功",
	"data": "{\"output\":{\"choices\":[{\"message\":{\"content\":\"{\\\"answer\\\":[{\\\"A\\\":\\\"左侧A柱盲区内可能有行人将要通过\\\"}],\\\"analysis\\\":\\\"在驾车时，A柱确实会形成一定的视觉盲区，尤其是在观察左右两侧路况时。题目中提到需要特别注意的情况，应是左侧A柱盲区内可能有行人将要通过，这是对驾驶员安全意识的考察，提醒驾驶员要注意盲区内的行人和车辆动态，确保行车安全。其他选项与当前情境下最需注意的重点不符。\\\"}\",\"role\":\"assistant\"},\"finish_reason\":\"stop\"}]},\"usage\":{\"total_tokens\":205,\"output_tokens\":99,\"input_tokens\":106,\"prompt_tokens_details\":{\"cached_tokens\":0}},\"request_id\":\"0aa78704-4ac1-9964-9c41-9ad3817bdbf8\"}",
	"trace_id": "daln7pz1tnfs"
}

格式化解析后获得以下内容
    {   
	"answer": [
		{
			"A": "左侧A柱盲区内可能有行人将要通过"
		}
	],
    "analysis":"在驾车时，A柱确实会形成一定的视觉盲区，尤其是在观察左右两侧路况时。题目中提到需要特别注意的情况，应是左侧A柱盲区内可能有行人将要通过，这是对驾驶员安全意识的考察，提醒驾驶员要注意盲区内的行人和车辆动态，确保行车安全。其他选项与当前情境下最需注意的重点不符。"},


得到qwen-plus的返回数据后，我们需要将之前ProcessContext保存的数据与qwen-plus返回的数据进行入库。

在返回数据前，已经获得的值如下

hash_key = 缓存键名字   //需要注意的是，前面生成的缓存键的名字与数据库存储的缓存键的值必须一致。这样后续回写redis时，才能保证redis的key与mysql的hash_key字段值对应上。
quest_type = 问题类型 
quest_content = 问题内容 
quest_options = 问题选项的json字符串  //需要注意的是无论是多选题还是单选题都需要json格式。
user_image = 用户提交的图片 URL 地址 
qwen_raw = qwen-vl-plus 返回的原始数据 
hash_raw = 缓存键名哈希前的原文
qwen_parsed = 请求qwen-plus前拼装的那个json字符串的原文


在得到qwen-plus返回数据后，我们又得到的值为 qwen_plus_raw = qwen_plus 返回的原始数据 
answer = 问题答案数组
analysis = 问题解析字符串

下面这俩字段也需要入库，这俩字段是管理员后续维护使用的字段
image_url = 问题对应的图片名称  //这里是不存在的，但是要将这个字段插入null。后续管理员手动补充
is_verified = 是否已经验证过，默认值为0


将上述值存入mysql后最终完整入库。然后准备回写redis

回写redis时从mysql取的值不应是所有字段，因为mysql的所有字段中包含了敏感信息，不应该暴露给用户。

最终给用户返回的值应该按如下示例的字段返回

[ { "quest_type": "多选题", "quest_content": "雾天跟车行驶,应如何安全驾驶?", "quest_options": { "A": "加大跟车距离,降低行驶速度", "B": "提前开启雾灯、危险报警闪光灯", "C": "以前车尾灯作为判断安全距离的参照物", "D": "按喇叭提示行车位置" }, "answer": { "A": "加大跟车距离,降低行驶速度", "B": "提前开启雾灯、危险报警闪光灯", "C": "以前车尾灯作为判断安全距离的参照物" }, "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。", "image_url": "",   "user_image": "https://www.qdq.cc.qdqdq.png",     "is_verified": "0" } ]

另外需要注意回写逻辑，先检查mysql中是否存在hash_key值相同的记录，如果存在需要将相同hash_key的记录拼成数组一起回写到redis的valve；

假设三个问题的hash_key相同，则将三个问题按照以下格式全部作为valve回写redis（虽然不存在相同值的键被存入mysql的合理性，但业务逻辑要求如此处理，因为在特殊情况下管理员会手动更改mysql记录的hash_key。以保证特殊场景使用。）

[ { "quest_type": "多选题", "quest_content": "雾天跟车行驶,应如何安全驾驶?", "options": { "A": "加大跟车距离,降低行驶速度", "B": "提前开启雾灯、危险报警闪光灯", "C": "以前车尾灯作为判断安全距离的参照物", "D": "按喇叭提示行车位置" }, "answer": { "A": "加大跟车距离,降低行驶速度", "B": "提前开启雾灯、危险报警闪光灯", "C": "以前车尾灯作为判断安全距离的参照物" }, "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。", "image_url": "",   "user_image": "https://www.qdq.cc.qdqdq.png",     "is_verified": "0" }, { "quest_type": "判断题", "quest_content": "驾驶机动车在雾天应使用远光灯提高能见度。", "options": { "A": "正确", "B": "错误" }, "answer": { "B": "错误" }, "analysis": "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯，因此本题错误。" }, { "quest_type": "单选题", "quest_content": "在高速公路上发生故障或事故，应如何设置警告标志？", "options": { "A": "在车后50米处设置", "B": "在车后100米处设置", "C": "在车后150米外设置", "D": "在车后200米外设置" }, "answer": { "C": "在车后150米外设置" }, "analysis": "在高速公路上，应在车辆后方150米以外设置警告标志，以提示后车注意减速，避免发生二次事故。", "image_url": "",   "user_image": "https://www.qdq.cc.qdqdq.png",     "is_verified": "0" } ]