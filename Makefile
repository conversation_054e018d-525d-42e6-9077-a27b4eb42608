# 项目名称
PROJECT_NAME := qwen-solve
BINARY_NAME := qwen-solve-server

# Go相关变量
GO := go
GOMOD := $(GO) mod
GOBUILD := $(GO) build
GOCLEAN := $(GO) clean
GOTEST := $(GO) test
GOGET := $(GO) get
GOFMT := $(GO) fmt

# 构建相关变量
BUILD_DIR := build
MAIN_PATH := cmd/server/main.go
LDFLAGS := -ldflags "-X main.Version=$(shell git describe --tags --always --dirty) -X main.BuildTime=$(shell date +%Y-%m-%d_%H:%M:%S)"

# 默认目标
.PHONY: all
all: clean deps fmt vet test build

# 安装依赖
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 格式化代码
.PHONY: fmt
fmt:
	$(GOFMT) ./...

# 代码检查
.PHONY: vet
vet:
	$(GO) vet ./...

# 运行测试
.PHONY: test
test:
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html

# 构建项目
.PHONY: build
build:
	mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 构建Linux版本
.PHONY: build-linux
build-linux:
	mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)

# 构建Windows版本
.PHONY: build-windows
build-windows:
	mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_PATH)

# 构建macOS版本
.PHONY: build-darwin
build-darwin:
	mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)

# 构建所有平台版本
.PHONY: build-all
build-all: build-linux build-windows build-darwin

# 运行项目
.PHONY: run
run:
	$(GO) run $(MAIN_PATH)

# 开发模式运行（使用air热重载）
.PHONY: dev
dev:
	air

# 清理构建文件
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 安装开发工具
.PHONY: install-tools
install-tools:
	$(GO) install github.com/cosmtrek/air@latest
	$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 代码质量检查
.PHONY: lint
lint:
	golangci-lint run

# 初始化数据库
.PHONY: init-db
init-db:
	mysql -h$(MYSQL_HOST) -P$(MYSQL_PORT) -u$(MYSQL_USERNAME) -p$(MYSQL_PASSWORD) $(MYSQL_DATABASE) < database_design.sql

# 创建环境变量文件
.PHONY: init-env
init-env:
	cp .env.example .env

# Docker相关
.PHONY: docker-build
docker-build:
	docker build -t $(PROJECT_NAME):latest .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --env-file .env $(PROJECT_NAME):latest

# 帮助信息
.PHONY: help
help:
	@echo "可用的命令:"
	@echo "  all          - 执行完整的构建流程（清理、依赖、格式化、检查、测试、构建）"
	@echo "  deps         - 下载并整理Go模块依赖"
	@echo "  fmt          - 格式化Go代码"
	@echo "  vet          - 运行Go vet代码检查"
	@echo "  test         - 运行测试"
	@echo "  test-coverage- 运行测试并生成覆盖率报告"
	@echo "  build        - 构建项目"
	@echo "  build-linux  - 构建Linux版本"
	@echo "  build-windows- 构建Windows版本"
	@echo "  build-darwin - 构建macOS版本"
	@echo "  build-all    - 构建所有平台版本"
	@echo "  run          - 运行项目"
	@echo "  dev          - 开发模式运行（需要安装air）"
	@echo "  clean        - 清理构建文件"
	@echo "  install-tools- 安装开发工具"
	@echo "  lint         - 代码质量检查"
	@echo "  init-db      - 初始化数据库"
	@echo "  init-env     - 创建环境变量文件"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  help         - 显示此帮助信息"
