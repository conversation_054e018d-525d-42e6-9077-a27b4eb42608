package service

import (
	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/pkg/utils"
)

// ImageService 图片验证服务接口
type ImageService interface {
	ValidateImage(ctx *context.ProcessContext) error
}

// imageService 图片验证服务实现
type imageService struct{}

// NewImageService 创建图片验证服务实例
func NewImageService() ImageService {
	return &imageService{}
}

// ValidateImage 验证图片是否可访问
func (s *imageService) ValidateImage(ctx *context.ProcessContext) error {
	// 验证图片URL是否可访问
	if err := utils.ValidateImageURL(ctx.UserImageURL); err != nil {
		ctx.SetError(err, model.ErrorMessages[model.CodeImageNotFound])
		return err
	}

	// 设置图片验证通过标志
	ctx.ImageValid = true
	
	return nil
}
