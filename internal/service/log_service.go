package service

import (
	"fmt"

	"qwen-solve/internal/context"
	"qwen-solve/internal/model"
	"qwen-solve/internal/repository"
)

// LogService 日志服务接口
type LogService interface {
	CreateRequestLog(ctx *context.ProcessContext) error
	GetRequestLogByID(id uint64) (*model.RequestLog, error)
	DeleteRequestLog(id uint64) error
	ListRequestLogs(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error)
	GetRequestLogStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error)
	GetErrorLogs(page, pageSize int) ([]*model.RequestLog, int64, error)
	GetRecentLogs(limit int) ([]*model.RequestLog, error)
}

// logService 日志服务实现
type logService struct {
	logRepo repository.RequestLogRepository
}

// NewLogService 创建日志服务实例
func NewLogService() LogService {
	return &logService{
		logRepo: repository.NewRequestLogRepository(),
	}
}

// CreateRequestLog 创建请求日志
func (s *logService) CreateRequestLog(ctx *context.ProcessContext) error {
	// 设置处理时间
	ctx.SetProcessingTime()
	
	// 转换为请求日志
	logReq := ctx.ToRequestLog()
	
	// 保存到数据库
	if err := s.logRepo.Create(logReq); err != nil {
		return fmt.Errorf("failed to create request log: %w", err)
	}
	
	return nil
}

// GetRequestLogByID 根据ID获取请求日志
func (s *logService) GetRequestLogByID(id uint64) (*model.RequestLog, error) {
	log, err := s.logRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get request log by id: %w", err)
	}
	
	if log == nil {
		return nil, fmt.Errorf("request log not found")
	}
	
	return log, nil
}

// DeleteRequestLog 删除请求日志（软删除）
func (s *logService) DeleteRequestLog(id uint64) error {
	if err := s.logRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete request log: %w", err)
	}
	
	return nil
}

// ListRequestLogs 获取请求日志列表
func (s *logService) ListRequestLogs(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	
	logs, total, err := s.logRepo.List(req)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list request logs: %w", err)
	}
	
	return logs, total, nil
}

// GetRequestLogStatistics 获取请求日志统计信息
func (s *logService) GetRequestLogStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error) {
	stats, err := s.logRepo.GetStatistics(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get request log statistics: %w", err)
	}
	
	return stats, nil
}

// BatchDeleteRequestLogs 批量删除请求日志
func (s *logService) BatchDeleteRequestLogs(ids []uint64) error {
	for _, id := range ids {
		if err := s.logRepo.Delete(id); err != nil {
			return fmt.Errorf("failed to delete request log %d: %w", id, err)
		}
	}
	
	return nil
}

// GetErrorLogs 获取错误日志
func (s *logService) GetErrorLogs(page, pageSize int) ([]*model.RequestLog, int64, error) {
	req := &model.RequestLogListRequest{
		Page:     page,
		PageSize: pageSize,
		Status:   "error",
	}

	return s.ListRequestLogs(req)
}

// GetRecentLogs 获取最近的日志
func (s *logService) GetRecentLogs(limit int) ([]*model.RequestLog, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	
	req := &model.RequestLogListRequest{
		Page:     1,
		PageSize: limit,
	}
	
	logs, _, err := s.ListRequestLogs(req)
	return logs, err
}


