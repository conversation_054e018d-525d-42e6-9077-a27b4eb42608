package context

import (
	"encoding/json"
	"qwen-solve/internal/model"
	"time"
)

// ProcessContext 处理上下文，贯穿整个业务流程
type ProcessContext struct {
	// 请求信息
	RequestID    string    `json:"request_id"`
	StartTime    time.Time `json:"start_time"`
	UserImageURL string    `json:"user_image_url"`
	RequestIP    string    `json:"request_ip"`
	UserAgent    string    `json:"user_agent"`

	// 图片验证结果
	ImageValid bool `json:"image_valid"`

	// qwen-vl-plus 相关数据
	QwenRawData string `json:"qwen_raw_data"` // 完整的原始数据

	// 解析后的题目数据
	QuestType    model.QuestionType    `json:"quest_type"`
	QuestContent string                `json:"quest_content"`
	QuestOptions map[string]string     `json:"quest_options"`

	// 缓存相关
	HashKey string `json:"hash_key"` // 缓存键名
	HashRaw string `json:"hash_raw"` // 哈希前的原文

	// 缓存命中情况
	RedisHit bool `json:"redis_hit"`
	MysqlHit bool `json:"mysql_hit"`

	// qwen-plus 相关数据
	QwenParsed  string `json:"qwen_parsed"`   // 请求qwen-plus前拼装的JSON字符串
	QwenPlusRaw string `json:"qwen_plus_raw"` // qwen-plus返回的原始数据

	// 解析后的答案数据
	Answer   map[string]string `json:"answer"`
	Analysis string            `json:"analysis"`

	// 错误信息
	Error        error  `json:"-"`
	ErrorMessage string `json:"error_message"`

	// 处理时间统计
	ProcessingTime int64 `json:"processing_time"` // 毫秒

	// 最终响应数据
	ResponseData []*model.QuestionResponse `json:"response_data"`
}

// NewProcessContext 创建新的处理上下文
func NewProcessContext(requestID, userImageURL, requestIP, userAgent string) *ProcessContext {
	return &ProcessContext{
		RequestID:    requestID,
		StartTime:    time.Now(),
		UserImageURL: userImageURL,
		RequestIP:    requestIP,
		UserAgent:    userAgent,
		QuestOptions: make(map[string]string),
		Answer:       make(map[string]string),
	}
}

// SetError 设置错误信息
func (ctx *ProcessContext) SetError(err error, message string) {
	ctx.Error = err
	ctx.ErrorMessage = message
}

// HasError 检查是否有错误
func (ctx *ProcessContext) HasError() bool {
	return ctx.Error != nil || ctx.ErrorMessage != ""
}

// GetProcessingTime 获取处理时间（毫秒）
func (ctx *ProcessContext) GetProcessingTime() int64 {
	if ctx.ProcessingTime > 0 {
		return ctx.ProcessingTime
	}
	return time.Since(ctx.StartTime).Milliseconds()
}

// SetProcessingTime 设置处理时间
func (ctx *ProcessContext) SetProcessingTime() {
	ctx.ProcessingTime = time.Since(ctx.StartTime).Milliseconds()
}

// ToRequestLog 转换为请求日志
func (ctx *ProcessContext) ToRequestLog() *model.CreateRequestLogRequest {
	var responseData *string
	if ctx.ResponseData != nil {
		if data, err := json.Marshal(ctx.ResponseData); err == nil {
			responseDataStr := string(data)
			responseData = &responseDataStr
		}
	}

	var errorMessage *string
	if ctx.ErrorMessage != "" {
		errorMessage = &ctx.ErrorMessage
	}

	processingTime := ctx.GetProcessingTime()

	return &model.CreateRequestLogRequest{
		RequestID:      ctx.RequestID,
		UserImageURL:   ctx.UserImageURL,
		ResponseData:   responseData,
		IsRedisHit:     ctx.RedisHit,
		IsMysqlHit:     ctx.MysqlHit,
		ErrorMessage:   errorMessage,
		ProcessingTime: &[]int{int(processingTime)}[0],
		RequestIP:      &ctx.RequestIP,
		UserAgent:      &ctx.UserAgent,
	}
}

// ToQuestion 转换为题目模型
func (ctx *ProcessContext) ToQuestion() *model.Question {
	// 转换选项为JSON格式
	questOptions := make(model.JSON)
	for k, v := range ctx.QuestOptions {
		questOptions[k] = v
	}

	// 转换答案为JSON格式
	answer := make(model.JSON)
	for k, v := range ctx.Answer {
		answer[k] = v
	}

	return &model.Question{
		HashKey:      ctx.HashKey,
		QuestType:    ctx.QuestType,
		QuestContent: ctx.QuestContent,
		QuestOptions: questOptions,
		Answer:       answer,
		Analysis:     ctx.Analysis,
		UserImage:    &ctx.UserImageURL,
		HashRaw:      &ctx.HashRaw,
		QwenRaw:      &ctx.QwenRawData,
		QwenParsed:   &ctx.QwenParsed,
		QwenPlusRaw:  &ctx.QwenPlusRaw,
		IsVerified:   false,
	}
}

// GetCacheSource 获取缓存来源
func (ctx *ProcessContext) GetCacheSource() string {
	if ctx.RedisHit {
		return "redis"
	}
	if ctx.MysqlHit {
		return "mysql"
	}
	return "qwen"
}

// IsCached 是否来自缓存
func (ctx *ProcessContext) IsCached() bool {
	return ctx.RedisHit || ctx.MysqlHit
}
