package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// QuestionType 题目类型枚举
type QuestionType string

const (
	QuestionTypeSingle   QuestionType = "单选题"
	QuestionTypeMultiple QuestionType = "多选题"
	QuestionTypeJudge    QuestionType = "判断题"
)

// IsValid 验证题目类型是否有效
func (qt QuestionType) IsValid() bool {
	switch qt {
	case QuestionTypeSingle, QuestionTypeMultiple, QuestionTypeJudge:
		return true
	default:
		return false
	}
}

// JSON 自定义JSON类型，用于存储JSON数据
type JSON map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into JSON", value)
	}

	return json.Unmarshal(bytes, j)
}

// Question 题库模型
type Question struct {
	ID           uint64       `json:"id" db:"id"`
	HashKey      string       `json:"hash_key" db:"hash_key"`
	QuestType    QuestionType `json:"quest_type" db:"quest_type"`
	QuestContent string       `json:"quest_content" db:"quest_content"`
	QuestOptions JSON         `json:"quest_options" db:"quest_options"`
	Answer       JSON         `json:"answer" db:"answer"`
	Analysis     string       `json:"analysis" db:"analysis"`
	UserImage    *string      `json:"user_image" db:"user_image"`
	ImageURL     *string      `json:"image_url" db:"image_url"`
	HashRaw      *string      `json:"hash_raw" db:"hash_raw"`
	QwenRaw      *string      `json:"qwen_raw" db:"qwen_raw"`
	QwenParsed   *string      `json:"qwen_parsed" db:"qwen_parsed"`
	QwenPlusRaw  *string      `json:"qwen_plus_raw" db:"qwen_plus_raw"`
	IsVerified   bool         `json:"is_verified" db:"is_verified"`
	CreatedAt    time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at" db:"updated_at"`
	DeletedAt    *time.Time   `json:"deleted_at" db:"deleted_at"`
}

// QuestionResponse 题目响应模型（返回给用户的数据）
type QuestionResponse struct {
	QuestType    QuestionType `json:"quest_type"`
	QuestContent string       `json:"quest_content"`
	QuestOptions JSON         `json:"quest_options"`
	Answer       JSON         `json:"answer"`
	Analysis     string       `json:"analysis"`
	ImageURL     string       `json:"image_url"`
	UserImage    string       `json:"user_image"`
	IsVerified   string       `json:"is_verified"`
}

// ToResponse 转换为响应模型
func (q *Question) ToResponse() *QuestionResponse {
	resp := &QuestionResponse{
		QuestType:    q.QuestType,
		QuestContent: q.QuestContent,
		QuestOptions: q.QuestOptions,
		Answer:       q.Answer,
		Analysis:     q.Analysis,
		ImageURL:     "",
		UserImage:    "",
		IsVerified:   "0",
	}

	if q.ImageURL != nil {
		resp.ImageURL = *q.ImageURL
	}
	if q.UserImage != nil {
		resp.UserImage = *q.UserImage
	}
	if q.IsVerified {
		resp.IsVerified = "1"
	}

	return resp
}

// CreateQuestionRequest 创建题目请求
type CreateQuestionRequest struct {
	QuestType    QuestionType `json:"quest_type" binding:"required"`
	QuestContent string       `json:"quest_content" binding:"required"`
	QuestOptions JSON         `json:"quest_options" binding:"required"`
	Answer       JSON         `json:"answer" binding:"required"`
	Analysis     string       `json:"analysis" binding:"required"`
	HashKey      string       `json:"hash_key" binding:"required"`
	ImageURL     *string      `json:"image_url"`
}

// UpdateQuestionRequest 更新题目请求
type UpdateQuestionRequest struct {
	QuestType    *QuestionType `json:"quest_type"`
	QuestContent *string       `json:"quest_content"`
	QuestOptions *JSON         `json:"quest_options"`
	Answer       *JSON         `json:"answer"`
	Analysis     *string       `json:"analysis"`
	ImageURL     *string       `json:"image_url"`
	IsVerified   *bool         `json:"is_verified"`
}

// QuestionListRequest 题目列表请求
type QuestionListRequest struct {
	Page       int           `form:"page" binding:"min=1"`
	PageSize   int           `form:"page_size" binding:"min=1,max=100"`
	QuestType  *QuestionType `form:"quest_type"`
	IsVerified *bool         `form:"is_verified"`
	Keyword    string        `form:"keyword"`
}

// QuestionListResponse 题目列表响应
type QuestionListResponse struct {
	List       []*Question `json:"list"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// TableName 返回表名
func (Question) TableName() string {
	return "qwen_solve_questions"
}
