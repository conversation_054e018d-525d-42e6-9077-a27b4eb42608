package model

// QwenDashScopeRequest qwen-vl-plus请求结构体
type QwenDashScopeRequest struct {
	Input      QwenInput      `json:"input"`
	Model      string         `json:"model"`
	Parameters QwenParameters `json:"parameters"`
}

// QwenInput 输入结构体
type QwenInput struct {
	Messages []QwenDashScopeMessage `json:"messages"`
}

// QwenDashScopeMessage 消息结构体
type QwenDashScopeMessage struct {
	Role    string                 `json:"role"`
	Content []QwenDashScopeContent `json:"content"`
}

// QwenDashScopeContent 内容结构体
type QwenDashScopeContent struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

// QwenParameters 参数结构体
type QwenParameters struct {
	PresencePenalty   int                `json:"presence_penalty"`
	RepetitionPenalty int                `json:"repetition_penalty"`
	ResponseFormat    QwenResponseFormat `json:"response_format"`
	Temperature       float64            `json:"temperature"`
	TopK              int                `json:"top_k"`
	TopP              float64            `json:"top_p"`
}

// QwenResponseFormat 响应格式结构体
type QwenResponseFormat struct {
	Type string `json:"type"`
}

// QwenDashScopeResponse qwen-vl-plus响应结构体
type QwenDashScopeResponse struct {
	Output    QwenOutput `json:"output"`
	Usage     QwenUsage  `json:"usage"`
	RequestID string     `json:"request_id"`
}

// QwenOutput 输出结构体
type QwenOutput struct {
	Choices []QwenChoice `json:"choices"`
}

// QwenChoice 选择结构体
type QwenChoice struct {
	Message      QwenMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

// QwenMessage 消息结构体
type QwenMessage struct {
	Content interface{} `json:"content"` // 可能是string或array
	Role    string      `json:"role"`
}

// QwenUsage 使用量结构体
type QwenUsage struct {
	TotalTokens  int `json:"total_tokens"`
	OutputTokens int `json:"output_tokens"`
	InputTokens  int `json:"input_tokens"`
}

// QwenTextGenerationRequest qwen-plus文本生成请求结构体
type QwenTextGenerationRequest struct {
	Model      string             `json:"model"`
	Input      QwenTextInput      `json:"input"`
	Parameters QwenTextParameters `json:"parameters"`
}

// QwenTextInput 文本输入结构体
type QwenTextInput struct {
	Messages []QwenDashScopeMessage `json:"messages"`
}

// QwenTextParameters 文本参数结构体
type QwenTextParameters struct {
	PresencePenalty   int                `json:"presence_penalty"`
	RepetitionPenalty int                `json:"repetition_penalty"`
	ResponseFormat    QwenResponseFormat `json:"response_format"`
	Temperature       float64            `json:"temperature"`
	TopK              int                `json:"top_k"`
	TopP              float64            `json:"top_p"`
	ResultFormat      string             `json:"result_format"`
}

// QwenVLResponse qwen-vl-plus识别结果
type QwenVLResponse struct {
	Qutext  string            `json:"qutext"`
	Options map[string]string `json:"options"`
}

// QwenPlusResponse qwen-plus答案生成结果
type QwenPlusResponse struct {
	Answer   interface{} `json:"answer"` // 可能是对象或数组
	Analysis string      `json:"analysis"`
}

// APIResponse 统一API响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	TraceID string      `json:"trace_id,omitempty"`
}

// SolveImageRequest 图片解题请求
type SolveImageRequest struct {
	ImageURL string `json:"image_url" binding:"required,url"`
}

// SolveImageResponse 图片解题响应
type SolveImageResponse struct {
	Questions []*QuestionResponse `json:"questions"`
	RequestID string              `json:"request_id"`
	Cached    bool                `json:"cached"`
	Source    string              `json:"source"` // redis, mysql, qwen
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// 错误码定义
const (
	// 成功
	CodeSuccess = 0

	// 客户端错误 (4xx)
	CodeBadRequest     = 400
	CodeUnauthorized   = 401
	CodeForbidden      = 403
	CodeNotFound       = 404
	CodeValidationFail = 422

	// 服务器错误 (5xx)
	CodeInternalError = 500
	CodeServiceError  = 502
	CodeTimeout       = 504

	// 业务错误 (1xxx)
	CodeImageNotFound     = 1001
	CodeImageInvalid      = 1002
	CodeQwenAPIError      = 1003
	CodeParseError        = 1004
	CodeDatabaseError     = 1005
	CodeCacheError        = 1006
	CodeQuestionTypeError = 1007
	CodeQuestionInvalid   = 1008
)

// 错误消息定义
var ErrorMessages = map[int]string{
	CodeSuccess:           "成功",
	CodeBadRequest:        "请求参数错误",
	CodeUnauthorized:      "未授权",
	CodeForbidden:         "禁止访问",
	CodeNotFound:          "资源不存在",
	CodeValidationFail:    "数据验证失败",
	CodeInternalError:     "内部服务器错误",
	CodeServiceError:      "服务错误",
	CodeTimeout:           "请求超时",
	CodeImageNotFound:     "图片不存在,上传失败,请联系管理员处理！",
	CodeImageInvalid:      "图片格式不正确",
	CodeQwenAPIError:      "AI服务调用失败",
	CodeParseError:        "数据解析失败",
	CodeDatabaseError:     "数据库操作失败",
	CodeCacheError:        "缓存操作失败",
	CodeQuestionTypeError: "图片不标准，题目类型未正确识别，请重新拍摄！",
	CodeQuestionInvalid:   "图片不标准，请正确拍摄！",
}
