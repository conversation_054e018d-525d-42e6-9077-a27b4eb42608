package model

import (
	"time"
)

// RequestLog 请求日志模型
type RequestLog struct {
	ID             uint64     `json:"id" db:"id"`
	RequestID      string     `json:"request_id" db:"request_id"`
	UserImageURL   string     `json:"user_image_url" db:"user_image_url"`
	ResponseData   *string    `json:"response_data" db:"response_data"`
	IsRedisHit     bool       `json:"is_redis_hit" db:"is_redis_hit"`
	IsMysqlHit     bool       `json:"is_mysql_hit" db:"is_mysql_hit"`
	ErrorMessage   *string    `json:"error_message" db:"error_message"`
	ProcessingTime *int       `json:"processing_time" db:"processing_time"` // 毫秒
	RequestIP      *string    `json:"request_ip" db:"request_ip"`
	UserAgent      *string    `json:"user_agent" db:"user_agent"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
	DeletedAt      *time.Time `json:"deleted_at" db:"deleted_at"`
}

// RequestLogResponse 请求日志响应模型
type RequestLogResponse struct {
	ID             uint64  `json:"id"`
	RequestID      string  `json:"request_id"`
	UserImageURL   string  `json:"user_image_url"`
	ResponseData   *string `json:"response_data"`
	IsRedisHit     bool    `json:"is_redis_hit"`
	IsMysqlHit     bool    `json:"is_mysql_hit"`
	ErrorMessage   *string `json:"error_message"`
	ProcessingTime *int    `json:"processing_time"`
	RequestIP      *string `json:"request_ip"`
	UserAgent      *string `json:"user_agent"`
	CreatedAt      string  `json:"created_at"`
	Status         string  `json:"status"` // success, error
}

// ToResponse 转换为响应模型
func (rl *RequestLog) ToResponse() *RequestLogResponse {
	resp := &RequestLogResponse{
		ID:             rl.ID,
		RequestID:      rl.RequestID,
		UserImageURL:   rl.UserImageURL,
		ResponseData:   rl.ResponseData,
		IsRedisHit:     rl.IsRedisHit,
		IsMysqlHit:     rl.IsMysqlHit,
		ErrorMessage:   rl.ErrorMessage,
		ProcessingTime: rl.ProcessingTime,
		RequestIP:      rl.RequestIP,
		UserAgent:      rl.UserAgent,
		CreatedAt:      rl.CreatedAt.Format("2006-01-02 15:04:05"),
		Status:         "success",
	}

	if rl.ErrorMessage != nil && *rl.ErrorMessage != "" {
		resp.Status = "error"
	}

	return resp
}

// CreateRequestLogRequest 创建请求日志请求
type CreateRequestLogRequest struct {
	RequestID      string  `json:"request_id"`
	UserImageURL   string  `json:"user_image_url"`
	ResponseData   *string `json:"response_data"`
	IsRedisHit     bool    `json:"is_redis_hit"`
	IsMysqlHit     bool    `json:"is_mysql_hit"`
	ErrorMessage   *string `json:"error_message"`
	ProcessingTime *int    `json:"processing_time"`
	RequestIP      *string `json:"request_ip"`
	UserAgent      *string `json:"user_agent"`
}

// RequestLogListRequest 请求日志列表请求
type RequestLogListRequest struct {
	Page       int     `form:"page" binding:"min=1"`
	PageSize   int     `form:"page_size" binding:"min=1,max=100"`
	RequestID  string  `form:"request_id"`
	Status     string  `form:"status"` // success, error, all
	IsRedisHit *bool   `form:"is_redis_hit"`
	IsMysqlHit *bool   `form:"is_mysql_hit"`
	StartDate  string  `form:"start_date"` // YYYY-MM-DD
	EndDate    string  `form:"end_date"`   // YYYY-MM-DD
	Keyword    string  `form:"keyword"`
}

// RequestLogListResponse 请求日志列表响应
type RequestLogListResponse struct {
	List       []*RequestLogResponse `json:"list"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
	Statistics *RequestLogStatistics `json:"statistics"`
}

// RequestLogStatistics 请求日志统计
type RequestLogStatistics struct {
	TotalRequests    int64   `json:"total_requests"`
	SuccessRequests  int64   `json:"success_requests"`
	ErrorRequests    int64   `json:"error_requests"`
	RedisHitCount    int64   `json:"redis_hit_count"`
	MysqlHitCount    int64   `json:"mysql_hit_count"`
	AvgProcessingTime float64 `json:"avg_processing_time"`
	SuccessRate      float64 `json:"success_rate"`
	RedisHitRate     float64 `json:"redis_hit_rate"`
	MysqlHitRate     float64 `json:"mysql_hit_rate"`
}

// TableName 返回表名
func (RequestLog) TableName() string {
	return "qwen_solve_request_logs"
}
