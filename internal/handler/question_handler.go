package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"qwen-solve/internal/model"
	"qwen-solve/internal/service"
	"qwen-solve/pkg/utils"
)

// QuestionHandler 题库管理处理器
type QuestionHandler struct {
	databaseService service.DatabaseService
}

// NewQuestionHandler 创建题库管理处理器实例
func NewQuestionHandler(databaseService service.DatabaseService) *QuestionHandler {
	return &QuestionHandler{
		databaseService: databaseService,
	}
}

// GetQuestions 获取题目列表接口
// @Summary 获取题目列表
// @Description 获取题目列表，支持分页和筛选
// @Tags questions
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param quest_type query string false "题目类型 单选题/多选题/判断题"
// @Param is_verified query bool false "是否已验证"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} model.APIResponse{data=model.QuestionListResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions [get]
func (h *QuestionHandler) GetQuestions(c *gin.Context) {
	// 构建请求参数
	req := &model.QuestionListRequest{
		Page:     1,
		PageSize: 20,
		Keyword:  c.Query("keyword"),
	}

	// 解析分页参数
	if p, exists := c.GetQuery("page"); exists {
		if pageInt, err := strconv.Atoi(p); err == nil && pageInt > 0 {
			req.Page = pageInt
		}
	}

	if ps, exists := c.GetQuery("page_size"); exists {
		if pageSizeInt, err := strconv.Atoi(ps); err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			req.PageSize = pageSizeInt
		}
	}

	// 解析题目类型
	if questType := c.Query("quest_type"); questType != "" {
		qt := model.QuestionType(questType)
		if qt.IsValid() {
			req.QuestType = &qt
		}
	}

	// 解析验证状态
	if isVerified, exists := c.GetQuery("is_verified"); exists {
		if verified, err := strconv.ParseBool(isVerified); err == nil {
			req.IsVerified = &verified
		}
	}

	// 获取题目列表
	questions, total, err := h.databaseService.ListQuestions(req)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	// 构建响应
	response := &model.QuestionListResponse{
		List:       questions,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}

// GetQuestionByID 根据ID获取题目接口
// @Summary 根据ID获取题目
// @Description 根据ID获取单个题目详情
// @Tags questions
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} model.APIResponse{data=model.Question} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 404 {object} model.APIResponse "题目不存在"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions/{id} [get]
func (h *QuestionHandler) GetQuestionByID(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, model.CodeBadRequest, "无效的题目ID")
		return
	}

	// 获取题目
	question, err := h.databaseService.GetQuestionByID(id)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	if question == nil {
		utils.ErrorResponse(c, model.CodeNotFound, "题目不存在")
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, question)
}

// CreateQuestion 创建题目接口
// @Summary 创建题目
// @Description 创建新的题目
// @Tags questions
// @Accept json
// @Produce json
// @Param request body model.CreateQuestionRequest true "创建题目请求"
// @Success 200 {object} model.APIResponse{data=model.Question} "创建成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions [post]
func (h *QuestionHandler) CreateQuestion(c *gin.Context) {
	var req model.CreateQuestionRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, err)
		return
	}

	// 创建题目
	question, err := h.databaseService.CreateQuestion(&req)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, question)
}

// UpdateQuestion 更新题目接口
// @Summary 更新题目
// @Description 更新指定题目的信息
// @Tags questions
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Param request body model.UpdateQuestionRequest true "更新题目请求"
// @Success 200 {object} model.APIResponse "更新成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 404 {object} model.APIResponse "题目不存在"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions/{id} [put]
func (h *QuestionHandler) UpdateQuestion(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, model.CodeBadRequest, "无效的题目ID")
		return
	}

	var req model.UpdateQuestionRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, err)
		return
	}

	// 更新题目
	if err := h.databaseService.UpdateQuestion(id, &req); err != nil {
		if err.Error() == "question not found or already deleted" {
			utils.ErrorResponse(c, model.CodeNotFound, "题目不存在或已被删除")
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "题目更新成功"})
}

// DeleteQuestion 删除题目接口
// @Summary 删除题目
// @Description 软删除指定的题目
// @Tags questions
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} model.APIResponse "删除成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 404 {object} model.APIResponse "题目不存在"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions/{id} [delete]
func (h *QuestionHandler) DeleteQuestion(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, model.CodeBadRequest, "无效的题目ID")
		return
	}

	// 删除题目
	if err := h.databaseService.DeleteQuestion(id); err != nil {
		if err.Error() == "question not found or already deleted" {
			utils.ErrorResponse(c, model.CodeNotFound, "题目不存在或已被删除")
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "题目删除成功"})
}

// SearchQuestions 搜索题目接口
// @Summary 搜索题目
// @Description 根据关键词搜索题目
// @Tags questions
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param quest_type query string false "题目类型"
// @Param is_verified query bool false "是否已验证"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} model.APIResponse{data=model.QuestionListResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/questions/search [get]
func (h *QuestionHandler) SearchQuestions(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		utils.ErrorResponse(c, model.CodeBadRequest, "搜索关键词不能为空")
		return
	}

	// 获取其他参数
	page := 1
	pageSize := 20

	if p, exists := c.GetQuery("page"); exists {
		if pageInt, err := strconv.Atoi(p); err == nil && pageInt > 0 {
			page = pageInt
		}
	}

	if ps, exists := c.GetQuery("page_size"); exists {
		if pageSizeInt, err := strconv.Atoi(ps); err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			pageSize = pageSizeInt
		}
	}

	var questType *model.QuestionType
	if qt := c.Query("quest_type"); qt != "" {
		qType := model.QuestionType(qt)
		if qType.IsValid() {
			questType = &qType
		}
	}

	var isVerified *bool
	if iv, exists := c.GetQuery("is_verified"); exists {
		if verified, err := strconv.ParseBool(iv); err == nil {
			isVerified = &verified
		}
	}

	// 搜索题目
	questions, total, err := h.databaseService.SearchQuestions(keyword, questType, isVerified, page, pageSize)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 构建响应
	response := &model.QuestionListResponse{
		List:       questions,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}
