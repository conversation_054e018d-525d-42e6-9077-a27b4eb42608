package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"qwen-solve/internal/model"
	"qwen-solve/internal/service"
	"qwen-solve/pkg/utils"
)

// LogHandler 日志管理处理器
type LogHandler struct {
	logService service.LogService
}

// NewLogHandler 创建日志管理处理器实例
func NewLogHandler(logService service.LogService) *LogHandler {
	return &LogHandler{
		logService: logService,
	}
}

// GetRequestLogs 获取请求日志列表接口
// @Summary 获取请求日志列表
// @Description 获取请求日志列表，支持分页和筛选
// @Tags logs
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param request_id query string false "请求ID"
// @Param status query string false "状态 success/error/all" default(all)
// @Param is_redis_hit query bool false "是否Redis命中"
// @Param is_mysql_hit query bool false "是否MySQL命中"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} model.APIResponse{data=model.RequestLogListResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs [get]
func (h *LogHandler) GetRequestLogs(c *gin.Context) {
	// 构建请求参数
	req := &model.RequestLogListRequest{
		Page:      1,
		PageSize:  20,
		RequestID: c.Query("request_id"),
		Status:    c.Query("status"),
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
		Keyword:   c.Query("keyword"),
	}

	// 解析分页参数
	if p, exists := c.GetQuery("page"); exists {
		if pageInt, err := strconv.Atoi(p); err == nil && pageInt > 0 {
			req.Page = pageInt
		}
	}

	if ps, exists := c.GetQuery("page_size"); exists {
		if pageSizeInt, err := strconv.Atoi(ps); err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			req.PageSize = pageSizeInt
		}
	}

	// 解析布尔参数
	if isRedisHit, exists := c.GetQuery("is_redis_hit"); exists {
		if hit, err := strconv.ParseBool(isRedisHit); err == nil {
			req.IsRedisHit = &hit
		}
	}

	if isMysqlHit, exists := c.GetQuery("is_mysql_hit"); exists {
		if hit, err := strconv.ParseBool(isMysqlHit); err == nil {
			req.IsMysqlHit = &hit
		}
	}

	// 获取日志列表
	logs, total, err := h.logService.ListRequestLogs(req)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 获取统计信息
	stats, err := h.logService.GetRequestLogStatistics(req)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 转换为响应格式
	var responses []*model.RequestLogResponse
	for _, log := range logs {
		responses = append(responses, log.ToResponse())
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	// 构建响应
	response := &model.RequestLogListResponse{
		List:       responses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		Statistics: stats,
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}

// GetRequestLogByID 根据ID获取请求日志接口
// @Summary 根据ID获取请求日志
// @Description 根据ID获取单个请求日志详情
// @Tags logs
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} model.APIResponse{data=model.RequestLogResponse} "成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 404 {object} model.APIResponse "日志不存在"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs/{id} [get]
func (h *LogHandler) GetRequestLogByID(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, model.CodeBadRequest, "无效的日志ID")
		return
	}

	// 获取日志
	log, err := h.logService.GetRequestLogByID(id)
	if err != nil {
		if err.Error() == "request log not found" {
			utils.ErrorResponse(c, model.CodeNotFound, "日志不存在")
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, log.ToResponse())
}

// DeleteRequestLog 删除请求日志接口
// @Summary 删除请求日志
// @Description 软删除指定的请求日志
// @Tags logs
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} model.APIResponse "删除成功"
// @Failure 400 {object} model.APIResponse "请求参数错误"
// @Failure 404 {object} model.APIResponse "日志不存在"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs/{id} [delete]
func (h *LogHandler) DeleteRequestLog(c *gin.Context) {
	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, model.CodeBadRequest, "无效的日志ID")
		return
	}

	// 删除日志
	if err := h.logService.DeleteRequestLog(id); err != nil {
		if err.Error() == "request log not found or already deleted" {
			utils.ErrorResponse(c, model.CodeNotFound, "日志不存在或已被删除")
		} else {
			utils.InternalErrorResponse(c, err)
		}
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, gin.H{"message": "日志删除成功"})
}

// GetLogStatistics 获取日志统计信息接口
// @Summary 获取日志统计信息
// @Description 获取请求日志的统计信息
// @Tags logs
// @Accept json
// @Produce json
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Success 200 {object} model.APIResponse{data=model.RequestLogStatistics} "成功"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs/statistics [get]
func (h *LogHandler) GetLogStatistics(c *gin.Context) {
	// 构建请求参数
	req := &model.RequestLogListRequest{
		StartDate: c.Query("start_date"),
		EndDate:   c.Query("end_date"),
	}

	// 获取统计信息
	stats, err := h.logService.GetRequestLogStatistics(req)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 返回成功响应
	utils.SuccessResponse(c, stats)
}

// GetErrorLogs 获取错误日志接口
// @Summary 获取错误日志
// @Description 获取所有错误日志
// @Tags logs
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} model.APIResponse{data=model.RequestLogListResponse} "成功"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs/errors [get]
func (h *LogHandler) GetErrorLogs(c *gin.Context) {
	// 获取分页参数
	page := 1
	pageSize := 20

	if p, exists := c.GetQuery("page"); exists {
		if pageInt, err := strconv.Atoi(p); err == nil && pageInt > 0 {
			page = pageInt
		}
	}

	if ps, exists := c.GetQuery("page_size"); exists {
		if pageSizeInt, err := strconv.Atoi(ps); err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			pageSize = pageSizeInt
		}
	}

	// 获取错误日志
	logs, total, err := h.logService.GetErrorLogs(page, pageSize)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 转换为响应格式
	var responses []*model.RequestLogResponse
	for _, log := range logs {
		responses = append(responses, log.ToResponse())
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	// 构建响应
	response := map[string]interface{}{
		"list":        responses,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": totalPages,
	}

	// 返回成功响应
	utils.SuccessResponse(c, response)
}

// GetRecentLogs 获取最近日志接口
// @Summary 获取最近日志
// @Description 获取最近的请求日志
// @Tags logs
// @Accept json
// @Produce json
// @Param limit query int false "数量限制" default(10)
// @Success 200 {object} model.APIResponse{data=[]model.RequestLogResponse} "成功"
// @Failure 500 {object} model.APIResponse "服务器错误"
// @Router /api/v1/logs/recent [get]
func (h *LogHandler) GetRecentLogs(c *gin.Context) {
	// 获取限制参数
	limit := 10
	if l, exists := c.GetQuery("limit"); exists {
		if limitInt, err := strconv.Atoi(l); err == nil && limitInt > 0 && limitInt <= 100 {
			limit = limitInt
		}
	}

	// 获取最近日志
	logs, err := h.logService.GetRecentLogs(limit)
	if err != nil {
		utils.InternalErrorResponse(c, err)
		return
	}

	// 转换为响应格式
	var responses []*model.RequestLogResponse
	for _, log := range logs {
		responses = append(responses, log.ToResponse())
	}

	// 返回成功响应
	utils.SuccessResponse(c, responses)
}
