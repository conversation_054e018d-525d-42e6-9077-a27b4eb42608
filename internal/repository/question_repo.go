package repository

import (
	"database/sql"
	"fmt"
	"strings"

	"qwen-solve/internal/model"
	"qwen-solve/pkg/database"
)

// QuestionRepository 题库数据访问接口
type QuestionRepository interface {
	Create(question *model.Question) error
	GetByID(id uint64) (*model.Question, error)
	GetByHashKey(hashKey string) ([]*model.Question, error)
	Update(id uint64, req *model.UpdateQuestionRequest) error
	Delete(id uint64) error
	List(req *model.QuestionListRequest) ([]*model.Question, int64, error)
	GetAllByHashKey(hashKey string) ([]*model.Question, error)
}

// questionRepository 题库数据访问实现
type questionRepository struct {
	db *sql.DB
}

// NewQuestionRepository 创建题库数据访问实例
func NewQuestionRepository() QuestionRepository {
	return &questionRepository{
		db: database.GetDB(),
	}
}

// Create 创建题目
func (r *questionRepository) Create(question *model.Question) error {
	query := `
		INSERT INTO qwen_solve_questions (
			hash_key, quest_type, quest_content, quest_options, answer, analysis,
			user_image, image_url, hash_raw, qwen_raw, qwen_parsed, qwen_plus_raw, is_verified
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.Exec(query,
		question.HashKey,
		question.QuestType,
		question.QuestContent,
		question.QuestOptions,
		question.Answer,
		question.Analysis,
		question.UserImage,
		question.ImageURL,
		question.HashRaw,
		question.QwenRaw,
		question.QwenParsed,
		question.QwenPlusRaw,
		question.IsVerified,
	)

	if err != nil {
		return fmt.Errorf("failed to create question: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert id: %w", err)
	}

	question.ID = uint64(id)
	return nil
}

// GetByID 根据ID获取题目
func (r *questionRepository) GetByID(id uint64) (*model.Question, error) {
	query := `
		SELECT id, hash_key, quest_type, quest_content, quest_options, answer, analysis,
			   user_image, image_url, hash_raw, qwen_raw, qwen_parsed, qwen_plus_raw, 
			   is_verified, created_at, updated_at, deleted_at
		FROM qwen_solve_questions 
		WHERE id = ? AND deleted_at IS NULL
	`

	question := &model.Question{}
	err := r.db.QueryRow(query, id).Scan(
		&question.ID,
		&question.HashKey,
		&question.QuestType,
		&question.QuestContent,
		&question.QuestOptions,
		&question.Answer,
		&question.Analysis,
		&question.UserImage,
		&question.ImageURL,
		&question.HashRaw,
		&question.QwenRaw,
		&question.QwenParsed,
		&question.QwenPlusRaw,
		&question.IsVerified,
		&question.CreatedAt,
		&question.UpdatedAt,
		&question.DeletedAt,
	)

	if err != nil {
		if database.IsNoRowsError(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get question by id: %w", err)
	}

	return question, nil
}

// GetByHashKey 根据哈希键获取题目（返回第一个匹配的）
func (r *questionRepository) GetByHashKey(hashKey string) ([]*model.Question, error) {
	return r.GetAllByHashKey(hashKey)
}

// GetAllByHashKey 根据哈希键获取所有匹配的题目
func (r *questionRepository) GetAllByHashKey(hashKey string) ([]*model.Question, error) {
	query := `
		SELECT id, hash_key, quest_type, quest_content, quest_options, answer, analysis,
			   user_image, image_url, hash_raw, qwen_raw, qwen_parsed, qwen_plus_raw, 
			   is_verified, created_at, updated_at, deleted_at
		FROM qwen_solve_questions 
		WHERE hash_key = ? AND deleted_at IS NULL
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(query, hashKey)
	if err != nil {
		return nil, fmt.Errorf("failed to query questions by hash key: %w", err)
	}
	defer rows.Close()

	var questions []*model.Question
	for rows.Next() {
		question := &model.Question{}
		err := rows.Scan(
			&question.ID,
			&question.HashKey,
			&question.QuestType,
			&question.QuestContent,
			&question.QuestOptions,
			&question.Answer,
			&question.Analysis,
			&question.UserImage,
			&question.ImageURL,
			&question.HashRaw,
			&question.QwenRaw,
			&question.QwenParsed,
			&question.QwenPlusRaw,
			&question.IsVerified,
			&question.CreatedAt,
			&question.UpdatedAt,
			&question.DeletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question: %w", err)
		}
		questions = append(questions, question)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate questions: %w", err)
	}

	return questions, nil
}

// Update 更新题目
func (r *questionRepository) Update(id uint64, req *model.UpdateQuestionRequest) error {
	var setParts []string
	var args []interface{}

	if req.QuestType != nil {
		setParts = append(setParts, "quest_type = ?")
		args = append(args, *req.QuestType)
	}
	if req.QuestContent != nil {
		setParts = append(setParts, "quest_content = ?")
		args = append(args, *req.QuestContent)
	}
	if req.QuestOptions != nil {
		setParts = append(setParts, "quest_options = ?")
		args = append(args, *req.QuestOptions)
	}
	if req.Answer != nil {
		setParts = append(setParts, "answer = ?")
		args = append(args, *req.Answer)
	}
	if req.Analysis != nil {
		setParts = append(setParts, "analysis = ?")
		args = append(args, *req.Analysis)
	}
	if req.ImageURL != nil {
		setParts = append(setParts, "image_url = ?")
		args = append(args, *req.ImageURL)
	}
	if req.IsVerified != nil {
		setParts = append(setParts, "is_verified = ?")
		args = append(args, *req.IsVerified)
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE qwen_solve_questions 
		SET %s 
		WHERE id = ? AND deleted_at IS NULL
	`, strings.Join(setParts, ", "))

	result, err := r.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update question: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("question not found or already deleted")
	}

	return nil
}

// Delete 软删除题目
func (r *questionRepository) Delete(id uint64) error {
	query := `
		UPDATE qwen_solve_questions 
		SET deleted_at = NOW() 
		WHERE id = ? AND deleted_at IS NULL
	`

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete question: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("question not found or already deleted")
	}

	return nil
}

// List 获取题目列表
func (r *questionRepository) List(req *model.QuestionListRequest) ([]*model.Question, int64, error) {
	// 构建WHERE条件
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "deleted_at IS NULL")

	if req.QuestType != nil {
		conditions = append(conditions, "quest_type = ?")
		args = append(args, *req.QuestType)
	}

	if req.IsVerified != nil {
		conditions = append(conditions, "is_verified = ?")
		args = append(args, *req.IsVerified)
	}

	if req.Keyword != "" {
		conditions = append(conditions, "(quest_content LIKE ? OR analysis LIKE ?)")
		keyword := "%" + req.Keyword + "%"
		args = append(args, keyword, keyword)
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM qwen_solve_questions " + whereClause
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count questions: %w", err)
	}

	// 获取列表数据
	offset, limit := database.Paginate(req.Page, req.PageSize)
	listQuery := fmt.Sprintf(`
		SELECT id, hash_key, quest_type, quest_content, quest_options, answer, analysis,
			   user_image, image_url, hash_raw, qwen_raw, qwen_parsed, qwen_plus_raw, 
			   is_verified, created_at, updated_at, deleted_at
		FROM qwen_solve_questions 
		%s 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?
	`, whereClause)

	listArgs := append(args, limit, offset)
	rows, err := r.db.Query(listQuery, listArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query questions: %w", err)
	}
	defer rows.Close()

	var questions []*model.Question
	for rows.Next() {
		question := &model.Question{}
		err := rows.Scan(
			&question.ID,
			&question.HashKey,
			&question.QuestType,
			&question.QuestContent,
			&question.QuestOptions,
			&question.Answer,
			&question.Analysis,
			&question.UserImage,
			&question.ImageURL,
			&question.HashRaw,
			&question.QwenRaw,
			&question.QwenParsed,
			&question.QwenPlusRaw,
			&question.IsVerified,
			&question.CreatedAt,
			&question.UpdatedAt,
			&question.DeletedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan question: %w", err)
		}
		questions = append(questions, question)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("failed to iterate questions: %w", err)
	}

	return questions, total, nil
}
