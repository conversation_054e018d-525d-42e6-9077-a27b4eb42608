package repository

import (
	"database/sql"
	"fmt"
	"strings"

	"qwen-solve/internal/model"
	"qwen-solve/pkg/database"
)

// RequestLogRepository 请求日志数据访问接口
type RequestLogRepository interface {
	Create(log *model.CreateRequestLogRequest) error
	GetByID(id uint64) (*model.RequestLog, error)
	Delete(id uint64) error
	List(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error)
	GetStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error)
}

// requestLogRepository 请求日志数据访问实现
type requestLogRepository struct {
	db *sql.DB
}

// NewRequestLogRepository 创建请求日志数据访问实例
func NewRequestLogRepository() RequestLogRepository {
	return &requestLogRepository{
		db: database.GetDB(),
	}
}

// Create 创建请求日志
func (r *requestLogRepository) Create(req *model.CreateRequestLogRequest) error {
	query := `
		INSERT INTO qwen_solve_request_logs (
			request_id, user_image_url, response_data, is_redis_hit, is_mysql_hit,
			error_message, processing_time, request_ip, user_agent
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.Exec(query,
		req.RequestID,
		req.UserImageURL,
		req.ResponseData,
		req.IsRedisHit,
		req.IsMysqlHit,
		req.ErrorMessage,
		req.ProcessingTime,
		req.RequestIP,
		req.UserAgent,
	)

	if err != nil {
		return fmt.Errorf("failed to create request log: %w", err)
	}

	return nil
}

// GetByID 根据ID获取请求日志
func (r *requestLogRepository) GetByID(id uint64) (*model.RequestLog, error) {
	query := `
		SELECT id, request_id, user_image_url, response_data, is_redis_hit, is_mysql_hit,
			   error_message, processing_time, request_ip, user_agent, created_at, deleted_at
		FROM qwen_solve_request_logs 
		WHERE id = ? AND deleted_at IS NULL
	`

	log := &model.RequestLog{}
	err := r.db.QueryRow(query, id).Scan(
		&log.ID,
		&log.RequestID,
		&log.UserImageURL,
		&log.ResponseData,
		&log.IsRedisHit,
		&log.IsMysqlHit,
		&log.ErrorMessage,
		&log.ProcessingTime,
		&log.RequestIP,
		&log.UserAgent,
		&log.CreatedAt,
		&log.DeletedAt,
	)

	if err != nil {
		if database.IsNoRowsError(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get request log by id: %w", err)
	}

	return log, nil
}

// Delete 软删除请求日志
func (r *requestLogRepository) Delete(id uint64) error {
	query := `
		UPDATE qwen_solve_request_logs 
		SET deleted_at = NOW() 
		WHERE id = ? AND deleted_at IS NULL
	`

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete request log: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("request log not found or already deleted")
	}

	return nil
}

// List 获取请求日志列表
func (r *requestLogRepository) List(req *model.RequestLogListRequest) ([]*model.RequestLog, int64, error) {
	// 构建WHERE条件
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "deleted_at IS NULL")

	if req.RequestID != "" {
		conditions = append(conditions, "request_id = ?")
		args = append(args, req.RequestID)
	}

	if req.Status != "" && req.Status != "all" {
		if req.Status == "success" {
			conditions = append(conditions, "(error_message IS NULL OR error_message = '')")
		} else if req.Status == "error" {
			conditions = append(conditions, "(error_message IS NOT NULL AND error_message != '')")
		}
	}

	if req.IsRedisHit != nil {
		conditions = append(conditions, "is_redis_hit = ?")
		args = append(args, *req.IsRedisHit)
	}

	if req.IsMysqlHit != nil {
		conditions = append(conditions, "is_mysql_hit = ?")
		args = append(args, *req.IsMysqlHit)
	}

	if req.StartDate != "" {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, req.StartDate+" 00:00:00")
	}

	if req.EndDate != "" {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, req.EndDate+" 23:59:59")
	}

	if req.Keyword != "" {
		conditions = append(conditions, "(user_image_url LIKE ? OR error_message LIKE ?)")
		keyword := "%" + req.Keyword + "%"
		args = append(args, keyword, keyword)
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM qwen_solve_request_logs " + whereClause
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count request logs: %w", err)
	}

	// 获取列表数据
	offset, limit := database.Paginate(req.Page, req.PageSize)
	listQuery := fmt.Sprintf(`
		SELECT id, request_id, user_image_url, response_data, is_redis_hit, is_mysql_hit,
			   error_message, processing_time, request_ip, user_agent, created_at, deleted_at
		FROM qwen_solve_request_logs 
		%s 
		ORDER BY created_at DESC 
		LIMIT ? OFFSET ?
	`, whereClause)

	listArgs := append(args, limit, offset)
	rows, err := r.db.Query(listQuery, listArgs...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query request logs: %w", err)
	}
	defer rows.Close()

	var logs []*model.RequestLog
	for rows.Next() {
		log := &model.RequestLog{}
		err := rows.Scan(
			&log.ID,
			&log.RequestID,
			&log.UserImageURL,
			&log.ResponseData,
			&log.IsRedisHit,
			&log.IsMysqlHit,
			&log.ErrorMessage,
			&log.ProcessingTime,
			&log.RequestIP,
			&log.UserAgent,
			&log.CreatedAt,
			&log.DeletedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan request log: %w", err)
		}
		logs = append(logs, log)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("failed to iterate request logs: %w", err)
	}

	return logs, total, nil
}

// GetStatistics 获取请求日志统计信息
func (r *requestLogRepository) GetStatistics(req *model.RequestLogListRequest) (*model.RequestLogStatistics, error) {
	// 构建WHERE条件（复用List方法的逻辑）
	var conditions []string
	var args []interface{}

	conditions = append(conditions, "deleted_at IS NULL")

	if req.StartDate != "" {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, req.StartDate+" 00:00:00")
	}

	if req.EndDate != "" {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, req.EndDate+" 23:59:59")
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	query := fmt.Sprintf(`
		SELECT
			COUNT(*) as total_requests,
			COALESCE(SUM(CASE WHEN (error_message IS NULL OR error_message = '') THEN 1 ELSE 0 END), 0) as success_requests,
			COALESCE(SUM(CASE WHEN (error_message IS NOT NULL AND error_message != '') THEN 1 ELSE 0 END), 0) as error_requests,
			COALESCE(SUM(CASE WHEN is_redis_hit = 1 THEN 1 ELSE 0 END), 0) as redis_hit_count,
			COALESCE(SUM(CASE WHEN is_mysql_hit = 1 THEN 1 ELSE 0 END), 0) as mysql_hit_count,
			COALESCE(AVG(CASE WHEN processing_time IS NOT NULL THEN processing_time ELSE 0 END), 0) as avg_processing_time
		FROM qwen_solve_request_logs
		%s
	`, whereClause)

	stats := &model.RequestLogStatistics{}
	err := r.db.QueryRow(query, args...).Scan(
		&stats.TotalRequests,
		&stats.SuccessRequests,
		&stats.ErrorRequests,
		&stats.RedisHitCount,
		&stats.MysqlHitCount,
		&stats.AvgProcessingTime,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get request log statistics: %w", err)
	}

	// 计算比率
	if stats.TotalRequests > 0 {
		stats.SuccessRate = float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100
		stats.RedisHitRate = float64(stats.RedisHitCount) / float64(stats.TotalRequests) * 100
		stats.MysqlHitRate = float64(stats.MysqlHitCount) / float64(stats.TotalRequests) * 100
	}

	return stats, nil
}
