
- 在请求qwen-plus前，需要将之前ProcessContext保存的数据进行拼接为一个字符串，然后作为qwen-plus的输入内容。

  示例如下；
  {
  "quest_type": "多选题",
  "quest_content": "石某驾驶低速载货机动车，运载4.05吨货物（核载1.2吨），行驶至某省道在越过道路中心线超越前方同向行驶的机动车时，与对向正常行驶的中型客车（乘载12人，核载11人）正面相撞，造成10人死亡、2人受伤。此事故中的违法行为是什么？",
  "quest_option": {
    "A": "客车超员",
    "B": "货车超载",
    "C": "货车违法超车",
    "D": "客车驾驶人疲劳驾驶"
  }
}

  {
  "quest_type": "判断题",
  "quest_content": "石某驾驶低速载货机动车，运载4.05吨货物（核载1.2吨），行驶至某省道在越过道路中心线超越前方同向行驶的机动车时，与对向正常行驶的中型客车（乘载12人，核载11人）正面相撞，造成10人死亡、2人受伤。此事故中的违法行为是什么？",
  "quest_option": {
    "Y": "正确",
    "N": "错误"
  }
}

如上示例内容发送给qwen-plus；

- 使用DashScope模式请求qwen-plus模型，得到qwen的返回数据。


- 请求地址为；https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

- 请求体如下；

	request := model.QwenTextGenerationRequest{
		Model: "qwen-plus", // 使用qwen-plus模型
		Input: model.QwenTextInput{
			Messages: []model.QwenDashScopeMessage{
				{
					Role: "system",
					Content: []model.QwenDashScopeContent{
						{
                            Text: "严格标准返回json格式。示例{\"answer\":[{\"正确选项\":\"选项内容\"}],\"analysis\":\"答案解析\"}",
						},
					},
				},
				{
					Role: "user",
					Content: []model.QwenDashScopeContent{
						{
							Text: "必须给出权威答案,answer字段的格式必须正确,选项字母为键，选项内容为值" + questionText + "",
						},
					},
				},
			},
		},
		Parameters: model.QwenTextParameters{
			PresencePenalty:   1,
			RepetitionPenalty: 1,
			ResponseFormat: model.QwenResponseFormat{
				Type: "json_object",
			},
			Temperature: 0.2,
			TopK:        1,
			TopP:        0.01,
			ResultFormat: "message",
		},
	}
