# Qwen Solve API 生产环境配置模板
# 复制此文件为 .env 并填入实际配置值

# ===========================================
# 必需配置 - 请务必修改以下配置
# ===========================================

# Qwen API配置（必须修改）
QWEN_KEY=your-qwen-api-key-here

# MySQL数据库配置（必须修改）
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USERNAME=your-mysql-username
MYSQL_PASSWORD=your-mysql-password
MYSQL_DATABASE=your-database-name

# Redis配置（必须修改）
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# ===========================================
# 可选配置 - 可根据需要调整
# ===========================================

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30

# 数据库连接池配置
MYSQL_CHARSET=utf8mb4
DB_MAX_OPEN_CONNS=100
DB_MAX_IDLE_CONNS=10
DB_CONN_MAX_LIFETIME=3600

# Redis连接配置
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5
REDIS_DIAL_TIMEOUT=5
REDIS_READ_TIMEOUT=3
REDIS_WRITE_TIMEOUT=3
REDIS_IDLE_TIMEOUT=300

# Qwen API配置
QWEN_BASE_URL=https://dashscope.aliyuncs.com
QWEN_TIMEOUT=60
QWEN_MAX_RETRIES=3

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=file
LOG_FILENAME=/var/log/qwen-solve/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=5
LOG_MAX_AGE=30
LOG_COMPRESS=true
