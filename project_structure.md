# Qwen Solve API 项目架构设计

## 项目目录结构

```
qwen-solve/
├── cmd/                          # 应用程序入口
│   └── server/
│       └── main.go              # 主程序入口
├── internal/                     # 内部包(不对外暴露)
│   ├── config/                  # 配置管理
│   │   ├── config.go           # 配置结构体和加载
│   │   └── database.go         # 数据库配置
│   ├── handler/                 # HTTP处理器
│   │   ├── solve_handler.go    # 图片解题处理器
│   │   ├── log_handler.go      # 日志管理处理器
│   │   └── question_handler.go # 题库管理处理器
│   ├── service/                 # 业务逻辑层
│   │   ├── solve_service.go    # 解题业务逻辑
│   │   ├── qwen_service.go     # Qwen API服务
│   │   ├── cache_service.go    # 缓存服务
│   │   ├── image_service.go    # 图片验证服务
│   │   └── log_service.go      # 日志服务
│   ├── repository/              # 数据访问层
│   │   ├── question_repo.go    # 题库数据访问
│   │   ├── log_repo.go         # 日志数据访问
│   │   └── config_repo.go      # 配置数据访问
│   ├── model/                   # 数据模型
│   │   ├── question.go         # 题库模型
│   │   ├── request_log.go      # 请求日志模型
│   │   ├── qwen_types.go       # Qwen API类型定义
│   │   └── response.go         # 响应模型
│   ├── middleware/              # 中间件
│   │   ├── cors.go             # 跨域处理
│   │   ├── logger.go           # 日志中间件
│   │   └── recovery.go         # 错误恢复
│   └── context/                 # 上下文管理
│       └── process_context.go  # 处理上下文
├── pkg/                         # 公共包(可对外暴露)
│   ├── database/               # 数据库连接
│   │   ├── mysql.go           # MySQL连接
│   │   └── migration.go       # 数据库迁移
│   ├── redis/                  # Redis连接
│   │   └── client.go          # Redis客户端
│   ├── utils/                  # 工具函数
│   │   ├── hash.go            # 哈希工具
│   │   ├── validator.go       # 验证工具
│   │   └── response.go        # 响应工具
│   └── logger/                 # 日志工具
│       └── logger.go          # 日志配置
├── configs/                    # 配置文件
│   ├── config.yaml            # 主配置文件
│   └── config.example.yaml    # 配置示例文件
├── docs/                       # 文档
│   ├── api.md                 # API文档
│   └── deployment.md          # 部署文档
├── scripts/                    # 脚本文件
│   ├── build.sh              # 构建脚本
│   └── deploy.sh             # 部署脚本
├── .env.example               # 环境变量示例
├── .gitignore                # Git忽略文件
├── go.mod                    # Go模块文件
├── go.sum                    # Go依赖校验文件
├── Makefile                  # Make构建文件
├── README.md                 # 项目说明
└── database_design.sql       # 数据库设计文件
```

## 核心组件说明

### 1. ProcessContext (处理上下文)
贯穿整个业务流程的上下文对象，存储：
- user_imgurl: 用户图片URL
- qwen_raw_data: qwen-vl-plus原始返回数据
- quest_type: 题目类型
- quest_content: 题目内容
- quest_option: 题目选项
- hash_key: 缓存键名
- 其他处理过程中的临时数据

### 2. 核心业务方法
- **CallQwenVlPlus**: 调用qwen-vl-plus模型识别图片
- **FormatQwenData**: 解析qwen返回数据
- **CallQwenPlus**: 调用qwen-plus模型生成答案
- **SaveToDatabase**: 保存数据到MySQL
- **WriteToRedis**: 写入Redis缓存

### 3. 数据流转
1. 图片验证 → ProcessContext
2. qwen-vl-plus识别 → ProcessContext
3. 数据解析 → ProcessContext
4. 缓存键生成 → Redis查询
5. MySQL查询 → qwen-plus调用
6. 数据入库 → Redis回写
7. 响应返回

## 技术栈

- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **ORM**: 原生SQL + database/sql
- **配置管理**: Viper
- **日志**: Logrus
- **HTTP客户端**: 原生net/http

## 开发规范

1. **命名规范**
   - 包名: 小写字母
   - 文件名: 下划线分隔
   - 函数名: 驼峰命名
   - 常量: 大写字母+下划线

2. **错误处理**
   - 统一错误码定义
   - 错误信息国际化
   - 详细的错误日志记录

3. **代码组织**
   - 单一职责原则
   - 依赖注入
   - 接口抽象
   - 单元测试覆盖

4. **性能优化**
   - 连接池管理
   - 缓存策略
   - 并发控制
   - 资源释放
