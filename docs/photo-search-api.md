# 拍照搜题 API 接入文档

## 基础信息
- **服务地址**: `http://your-domain.com`
- **Content-Type**: `application/json`
- **请求方式**: `POST`

## 核心接口

### 拍照搜题
**接口地址**: `POST /api/v1/solve`

**功能说明**: 上传图片URL，系统自动识别题目并返回答案解析

#### 请求参数
```json
{
  "image_url": "http://example.com/question-image.jpg"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image_url | string | 是 | 题目图片的URL地址，必须可公开访问 |

#### 响应格式

##### 成功响应
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "questions": [
      {
        "quest_type": "单选题",
        "quest_content": "以下哪个选项是正确的？",
        "quest_options": {
          "A": "选项A内容",
          "B": "选项B内容",
          "C": "选项C内容",
          "D": "选项D内容"
        },
        "answer": {
          "A": "选项A内容"
        },
        "analysis": "答案解析：选择A是因为...",
        "image_url": "",
        "user_image": "http://example.com/question-image.jpg",
        "is_verified": "0"
      }
    ],
    "request_id": "req_1703123456789",
    "cached": true,
    "source": "redis"
  }
}
```

##### 多题目响应示例
当图片包含多个题目时，`questions` 数组会包含多个题目对象：

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "questions": [
      {
        "quest_type": "单选题",
        "quest_content": "第一题：以下哪个选项是正确的？",
        "quest_options": {
          "A": "选项A",
          "B": "选项B",
          "C": "选项C",
          "D": "选项D"
        },
        "answer": {
          "A": "选项A"
        },
        "analysis": "第一题解析...",
        "image_url": "",
        "user_image": "http://example.com/question-image.jpg",
        "is_verified": "0"
      },
      {
        "quest_type": "多选题",
        "quest_content": "第二题：以下哪些选项是正确的？",
        "quest_options": {
          "A": "选项A",
          "B": "选项B",
          "C": "选项C",
          "D": "选项D"
        },
        "answer": {
          "A": "选项A",
          "C": "选项C"
        },
        "analysis": "第二题解析...",
        "image_url": "",
        "user_image": "http://example.com/question-image.jpg",
        "is_verified": "0"
      }
    ],
    "request_id": "req_1703123456790",
    "cached": false,
    "source": "qwen"
  }
}
```

##### 错误响应
```json
{
  "code": 4001,
  "message": "图片无法访问或格式不支持"
}
```

#### 响应字段说明

**data 字段**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| questions | array | 题目数组，包含识别到的所有题目 |
| request_id | string | 请求唯一标识 |
| cached | boolean | 是否命中缓存 |
| source | string | 数据来源：redis(缓存)/mysql(数据库)/qwen(AI生成) |

**questions 数组中每个题目对象**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| quest_type | string | 题目类型：单选题/多选题/判断题/填空题/问答题 |
| quest_content | string | 题目内容 |
| quest_options | object | 题目选项，键为选项标识(A/B/C/D)，值为选项内容 |
| answer | object | 正确答案，格式与quest_options相同 |
| analysis | string | 答案解析 |
| image_url | string | 标准题目图片URL(通常为空) |
| user_image | string | 用户上传的原始图片URL |
| is_verified | string | 是否已验证：0(未验证)/1(已验证) |

## 使用示例

### cURL
```bash
curl -X POST http://your-domain.com/api/v1/solve \
  -H "Content-Type: application/json" \
  -d '{"image_url":"http://example.com/question.jpg"}'
```

### JavaScript
```javascript
const response = await fetch('/api/v1/solve', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    image_url: 'http://example.com/question.jpg'
  })
});

const result = await response.json();
if (result.code === 0) {
  // 处理成功响应
  const questions = result.data.questions;
  questions.forEach((question, index) => {
    console.log(`题目${index + 1}:`, question.quest_content);
    console.log('答案:', question.answer);
    console.log('解析:', question.analysis);
  });
} else {
  // 处理错误
  console.error('错误:', result.message);
}
```

### Python
```python
import requests
import json

url = "http://your-domain.com/api/v1/solve"
data = {
    "image_url": "http://example.com/question.jpg"
}

response = requests.post(url, json=data)
result = response.json()

if result['code'] == 0:
    questions = result['data']['questions']
    for i, question in enumerate(questions):
        print(f"题目{i+1}: {question['quest_content']}")
        print(f"答案: {question['answer']}")
        print(f"解析: {question['analysis']}")
else:
    print(f"错误: {result['message']}")
```

## 注意事项

1. **图片要求**:
   - 图片URL必须可公开访问
   - 支持格式：JPG、PNG、GIF、BMP、WEBP
   - 建议图片清晰度足够，文字可读

2. **性能说明**:
   - 首次识别需要调用AI模型，响应时间较长(10-30秒)
   - 相同图片会使用缓存，响应速度快(1-3秒)
   - 请求超时时间为60秒

3. **多题目处理**:
   - 系统会自动识别图片中的多个题目
   - 每个题目作为独立对象返回在questions数组中
   - 建议前端遍历questions数组处理所有题目

4. **错误码说明**:
   - `0`: 成功
   - `4001`: 图片无法访问或格式不支持
   - `4002`: 题目类型识别失败
   - `4003`: 题目内容无效
   - `5000`: 服务器内部错误

## 辅助接口

### 图片验证
**接口地址**: `POST /api/v1/solve/validate`

用于预先验证图片URL是否可访问，避免无效请求。

### 强制重新处理
**接口地址**: `POST /api/v1/solve/reprocess`

跳过缓存，强制重新识别和生成答案，参数格式与主接口相同。
