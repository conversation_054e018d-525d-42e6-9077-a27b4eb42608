# 生产环境部署指南

## 📦 可执行文件信息

**文件名**: `qwen-solve-server-linux-amd64`  
**平台**: Linux x86_64  
**大小**: ~14.5MB  
**类型**: 静态链接可执行文件  
**版本**: a7a6163-dirty  
**构建时间**: 2025-06-14_13:40:54  

## 🔧 环境变量配置

### 必需的环境变量

```bash
# Qwen API配置（必须）
export QWEN_KEY="your-qwen-api-key"

# 数据库配置（必须）
export MYSQL_HOST="your-mysql-host"
export MYSQL_PORT="3306"
export MYSQL_USERNAME="your-username"
export MYSQL_PASSWORD="your-password"
export MYSQL_DATABASE="your-database"

# Redis配置（必须）
export REDIS_HOST="your-redis-host"
export REDIS_PORT="6379"
export REDIS_PASSWORD="your-redis-password"
```

### 可选的环境变量

```bash
# 服务器配置
export SERVER_HOST="0.0.0.0"                    # 监听地址，默认 0.0.0.0
export SERVER_PORT="8080"                       # 监听端口，默认 8080
export GIN_MODE="release"                        # 运行模式：debug/release/test，生产环境建议 release
export SERVER_READ_TIMEOUT="30"                 # 读取超时（秒），默认 30
export SERVER_WRITE_TIMEOUT="30"                # 写入超时（秒），默认 30

# 数据库连接池配置
export MYSQL_CHARSET="utf8mb4"                  # 字符集，默认 utf8mb4
export DB_MAX_OPEN_CONNS="100"                  # 最大连接数，默认 100
export DB_MAX_IDLE_CONNS="10"                   # 最大空闲连接数，默认 10
export DB_CONN_MAX_LIFETIME="3600"              # 连接最大生存时间（秒），默认 3600

# Redis连接配置
export REDIS_DB="0"                             # Redis数据库编号，默认 0
export REDIS_POOL_SIZE="10"                     # 连接池大小，默认 10
export REDIS_MIN_IDLE_CONNS="5"                 # 最小空闲连接数，默认 5
export REDIS_DIAL_TIMEOUT="5"                   # 连接超时（秒），默认 5
export REDIS_READ_TIMEOUT="3"                   # 读取超时（秒），默认 3
export REDIS_WRITE_TIMEOUT="3"                  # 写入超时（秒），默认 3
export REDIS_IDLE_TIMEOUT="300"                 # 空闲超时（秒），默认 300

# Qwen API配置
export QWEN_BASE_URL="https://dashscope.aliyuncs.com"  # API基础URL，默认阿里云
export QWEN_TIMEOUT="60"                        # API超时（秒），默认 60
export QWEN_MAX_RETRIES="3"                     # 最大重试次数，默认 3

# 日志配置
export LOG_LEVEL="info"                         # 日志级别：debug/info/warn/error，默认 info
export LOG_FORMAT="json"                        # 日志格式：json/text，默认 json
export LOG_OUTPUT="stdout"                      # 日志输出：stdout/file，默认 stdout
export LOG_FILENAME="logs/app.log"              # 日志文件路径（当OUTPUT=file时），默认 logs/app.log
export LOG_MAX_SIZE="100"                       # 日志文件最大大小（MB），默认 100
export LOG_MAX_BACKUPS="5"                      # 保留的日志文件数量，默认 5
export LOG_MAX_AGE="30"                         # 日志文件保留天数，默认 30
export LOG_COMPRESS="true"                      # 是否压缩旧日志文件，默认 true
```

## 🚀 部署步骤

### 1. 准备服务器环境

```bash
# 创建应用目录
sudo mkdir -p /opt/qwen-solve
sudo mkdir -p /opt/qwen-solve/logs
sudo mkdir -p /var/log/qwen-solve

# 创建应用用户（推荐）
sudo useradd -r -s /bin/false qwen-solve
sudo chown -R qwen-solve:qwen-solve /opt/qwen-solve
sudo chown -R qwen-solve:qwen-solve /var/log/qwen-solve
```

### 2. 上传可执行文件

```bash
# 上传文件到服务器
scp build/qwen-solve-server-linux-amd64 user@your-server:/opt/qwen-solve/

# 设置执行权限
sudo chmod +x /opt/qwen-solve/qwen-solve-server-linux-amd64
sudo chown qwen-solve:qwen-solve /opt/qwen-solve/qwen-solve-server-linux-amd64
```

### 3. 创建环境变量文件

```bash
# 创建环境变量文件
sudo tee /opt/qwen-solve/.env << 'EOF'
# 必需配置
QWEN_KEY=your-qwen-api-key
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USERNAME=your-username
MYSQL_PASSWORD=your-password
MYSQL_DATABASE=your-database
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 生产环境配置
GIN_MODE=release
SERVER_PORT=8080
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=file
LOG_FILENAME=/var/log/qwen-solve/app.log
EOF

# 设置文件权限
sudo chmod 600 /opt/qwen-solve/.env
sudo chown qwen-solve:qwen-solve /opt/qwen-solve/.env
```

### 4. 创建systemd服务文件

```bash
sudo tee /etc/systemd/system/qwen-solve.service << 'EOF'
[Unit]
Description=Qwen Solve API Server
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=qwen-solve
Group=qwen-solve
WorkingDirectory=/opt/qwen-solve
ExecStart=/opt/qwen-solve/qwen-solve-server-linux-amd64
EnvironmentFile=/opt/qwen-solve/.env
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qwen-solve

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/qwen-solve

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
```

### 5. 启动服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable qwen-solve

# 启动服务
sudo systemctl start qwen-solve

# 检查服务状态
sudo systemctl status qwen-solve

# 查看日志
sudo journalctl -u qwen-solve -f
```

## 🔍 健康检查

```bash
# 检查服务是否正常运行
curl http://localhost:8080/health

# 预期响应
{
  "code": 0,
  "message": "成功",
  "data": {
    "status": "ok",
    "timestamp": **********,
    "version": "1.0.0"
  }
}
```

## 📊 监控和维护

### 日志查看

```bash
# 查看实时日志
sudo journalctl -u qwen-solve -f

# 查看最近的日志
sudo journalctl -u qwen-solve -n 100

# 查看应用日志文件（如果配置了文件输出）
sudo tail -f /var/log/qwen-solve/app.log
```

### 服务管理

```bash
# 重启服务
sudo systemctl restart qwen-solve

# 停止服务
sudo systemctl stop qwen-solve

# 查看服务状态
sudo systemctl status qwen-solve

# 禁用服务
sudo systemctl disable qwen-solve
```

### 性能监控

```bash
# 查看进程资源使用
ps aux | grep qwen-solve

# 查看端口监听状态
sudo netstat -tlnp | grep 8080

# 查看系统资源使用
top -p $(pgrep qwen-solve)
```

## 🔒 安全建议

1. **防火墙配置**：只开放必要的端口（如8080）
2. **反向代理**：建议使用Nginx作为反向代理
3. **HTTPS**：在生产环境中启用HTTPS
4. **访问控制**：限制API访问来源
5. **定期更新**：定期更新应用版本和系统补丁

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查配置文件
   sudo systemctl status qwen-solve
   sudo journalctl -u qwen-solve -n 50
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库配置和网络连通性
   mysql -h $MYSQL_HOST -P $MYSQL_PORT -u $MYSQL_USERNAME -p
   ```

3. **Redis连接失败**
   ```bash
   # 检查Redis配置和网络连通性
   redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping
   ```

4. **端口被占用**
   ```bash
   # 查看端口占用情况
   sudo lsof -i :8080
   ```

---

**部署完成后，请确保所有配置正确，并进行充分的测试！**
