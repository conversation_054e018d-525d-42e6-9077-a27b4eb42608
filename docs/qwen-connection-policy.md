# Qwen API 连接策略说明

## 📋 概述

本文档说明了Qwen API服务的HTTP连接策略配置，确保每次请求都使用新的TCP连接。

## 🎯 连接策略

### 当前配置：强制新连接

系统已配置为**每次请求都建立新的TCP连接**，完全禁用连接复用。

## 🔧 技术实现

### 1. Transport配置

```go
// 创建自定义Transport，禁用连接复用
transport := &http.Transport{
    // 禁用连接复用
    DisableKeepAlives:   true,                // 禁用Keep-Alive
    MaxIdleConns:        0,                   // 不保持空闲连接
    MaxIdleConnsPerHost: 0,                   // 每个主机不保持空闲连接
    IdleConnTimeout:     1 * time.Second,     // 立即关闭空闲连接
    
    // 连接超时配置
    DialContext: (&net.Dialer{
        Timeout:   10 * time.Second,          // 连接超时
        KeepAlive: -1,                        // 禁用Keep-Alive
    }).DialContext,
    
    // TLS配置
    TLSHandshakeTimeout:   10 * time.Second,
    ResponseHeaderTimeout: 30 * time.Second,
    ExpectContinueTimeout: 1 * time.Second,
}
```

### 2. 请求头配置

```go
// 设置请求头
req.Header.Set("Content-Type", "application/json")
req.Header.Set("Authorization", "Bearer "+s.config.APIKey)
req.Header.Set("Connection", "close")  // 强制关闭连接，确保每次都是新连接
```

## 📊 连接行为

### 每次请求的完整流程

```
请求1: 建立TCP连接 → TLS握手 → 发送请求 → 接收响应 → 立即关闭连接
请求2: 建立TCP连接 → TLS握手 → 发送请求 → 接收响应 → 立即关闭连接
请求3: 建立TCP连接 → TLS握手 → 发送请求 → 接收响应 → 立即关闭连接
...
```

### 关键特性

- ✅ **完全隔离**: 每次请求使用独立的TCP连接
- ✅ **无状态**: 连接之间没有任何状态共享
- ✅ **立即释放**: 请求完成后立即关闭连接
- ✅ **资源清理**: 不保留任何空闲连接

## ⚡ 性能影响

### 优势
- **连接隔离**: 避免连接状态污染
- **资源释放**: 及时释放网络资源
- **故障隔离**: 单个连接问题不影响其他请求

### 劣势
- **延迟增加**: 每次都需要TCP握手和TLS握手
- **资源消耗**: 频繁建立/销毁连接的开销
- **并发限制**: 可能受到系统连接数限制

### 性能对比

| 指标 | 连接复用 | 强制新连接 |
|------|----------|------------|
| 首次请求延迟 | ~200-500ms | ~200-500ms |
| 后续请求延迟 | ~50-150ms | ~200-500ms |
| 内存使用 | 中等 | 较低 |
| CPU使用 | 较低 | 较高 |
| 网络开销 | 较低 | 较高 |

## 🔍 验证方法

### 1. 网络连接监控

```bash
# 监控TCP连接状态
netstat -an | grep :443 | grep dashscope.aliyuncs.com

# 使用tcpdump监控连接建立
sudo tcpdump -i any host dashscope.aliyuncs.com
```

### 2. 应用日志

可以在代码中添加连接监控日志：

```go
// 在DialContext中添加日志
DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
    log.Printf("建立新连接到: %s", addr)
    return (&net.Dialer{
        Timeout:   10 * time.Second,
        KeepAlive: -1,
    }).DialContext(ctx, network, addr)
},
```

### 3. 性能测试

```bash
# 使用测试脚本验证连接行为
cd scripts
./photo_search_test.sh
```

## 🛠️ 配置管理

### 环境变量

当前配置通过代码硬编码，如需动态配置可添加以下环境变量：

```bash
# 连接策略配置
QWEN_DISABLE_KEEP_ALIVE=true
QWEN_MAX_IDLE_CONNS=0
QWEN_FORCE_NEW_CONNECTION=true
```

### 配置结构扩展

```go
type QwenConfig struct {
    APIKey              string        `json:"api_key"`
    BaseURL             string        `json:"base_url"`
    Timeout             time.Duration `json:"timeout"`
    MaxRetries          int           `json:"max_retries"`
    
    // 连接策略配置
    DisableKeepAlive    bool          `json:"disable_keep_alive"`
    ForceNewConnection  bool          `json:"force_new_connection"`
    MaxIdleConns        int           `json:"max_idle_conns"`
}
```

## 🚨 注意事项

### 1. 性能考虑
- 每次请求的延迟会增加100-300ms
- 高并发场景下可能影响整体吞吐量
- 建议在性能要求不高的场景使用

### 2. 资源限制
- 注意系统的最大文件描述符限制
- 监控TIME_WAIT状态的连接数量
- 避免在短时间内发起大量请求

### 3. 错误处理
- 连接建立失败的重试机制
- 网络超时的处理策略
- DNS解析失败的处理

## 🔄 切换回连接复用

如需恢复连接复用，可以将NewQwenService函数改回：

```go
func NewQwenService(cfg *config.QwenConfig) QwenService {
    return &qwenService{
        config: cfg,
        client: &http.Client{
            Timeout: cfg.Timeout,  // 使用默认Transport（支持连接复用）
        },
    }
}
```

并移除sendRequest中的`Connection: close`头。

## 📈 监控建议

### 关键指标
- 请求延迟分布
- 连接建立成功率
- 网络错误率
- 系统资源使用情况

### 告警阈值
- 平均响应时间 > 2秒
- 连接失败率 > 5%
- TIME_WAIT连接数 > 1000

## 📝 总结

当前配置确保每次Qwen API请求都使用全新的TCP连接，提供了最大的连接隔离性，但会带来一定的性能开销。这种配置适合对连接隔离要求较高、对性能要求相对宽松的场景。
