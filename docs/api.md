# Qwen Solve API 接口文档

## 基础信息

- **基础URL**: `http://your-domain.com/api/v1`
- **认证方式**: 无需认证
- **请求格式**: JSON
- **响应格式**: JSON

## 通用响应格式

### 成功响应
```json
{
  "code": 0,
  "message": "成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 1001,
  "message": "图片不存在,上传失败,请联系管理员处理！"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |
| 1001 | 图片不存在 |
| 1007 | 题目类型未正确识别 |
| 1008 | 图片不标准 |

## 接口列表

### 1. 健康检查

**接口地址**: `GET /health`

**接口描述**: 检查服务运行状态

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "status": "ok",
    "timestamp": **********,
    "version": "1.0.0"
  }
}
```

---

### 2. 图片解题

**接口地址**: `POST /api/v1/solve`

**接口描述**: 上传图片URL进行题目识别和解答

**请求参数**:
```json
{
  "image_url": "http://example.com/image.jpg"
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image_url | string | 是 | 图片URL地址 |

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "questions": [
      {
        "quest_type": "单选题",
        "quest_content": "以下哪个选项是正确的？",
        "quest_options": {
          "A": "选项A内容",
          "B": "选项B内容",
          "C": "选项C内容",
          "D": "选项D内容"
        },
        "answer": {
          "A": "选项A内容"
        },
        "analysis": "答案解析内容...",
        "image_url": "",
        "user_image": "http://example.com/image.jpg",
        "is_verified": "0"
      }
    ],
    "request_id": "req_**********789",
    "cached": true,
    "source": "redis"
  }
}
```

---

### 3. 验证图片

**接口地址**: `POST /api/v1/solve/validate`

**接口描述**: 验证图片URL是否可访问

**请求参数**:
```json
{
  "image_url": "http://example.com/image.jpg"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "message": "图片验证成功"
  }
}
```

---

### 4. 重新处理图片

**接口地址**: `POST /api/v1/solve/reprocess`

**接口描述**: 强制重新处理图片，不使用缓存

**请求参数**: 同图片解题接口

**响应示例**: 同图片解题接口

---

### 5. 获取解题历史

**接口地址**: `GET /api/v1/solve/history`

**接口描述**: 获取解题历史记录

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "request_id": "req_**********789",
        "user_image_url": "http://example.com/image.jpg",
        "is_redis_hit": true,
        "is_mysql_hit": false,
        "processing_time": 1500,
        "created_at": "2023-12-21 10:30:45",
        "status": "success"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

---

### 6. 获取解题统计

**接口地址**: `GET /api/v1/solve/statistics`

**接口描述**: 获取解题统计信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | string | 否 | 开始日期 YYYY-MM-DD |
| end_date | string | 否 | 结束日期 YYYY-MM-DD |

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "total_requests": 1000,
    "success_requests": 950,
    "error_requests": 50,
    "redis_hit_count": 800,
    "mysql_hit_count": 100,
    "avg_processing_time": 1200.5,
    "success_rate": 95.0,
    "redis_hit_rate": 80.0,
    "mysql_hit_rate": 10.0
  }
}
```

---

### 7. 获取请求日志

**接口地址**: `GET /api/v1/logs`

**接口描述**: 获取请求日志列表

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |
| request_id | string | 否 | 请求ID |
| status | string | 否 | 状态：success/error/all |
| is_redis_hit | bool | 否 | 是否Redis命中 |
| is_mysql_hit | bool | 否 | 是否MySQL命中 |
| start_date | string | 否 | 开始日期 |
| end_date | string | 否 | 结束日期 |
| keyword | string | 否 | 关键词搜索 |

**响应示例**: 同解题历史接口

---

### 8. 删除请求日志

**接口地址**: `DELETE /api/v1/logs/{id}`

**接口描述**: 软删除指定的请求日志

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 日志ID |

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "message": "日志删除成功"
  }
}
```

---

### 9. 获取题目列表

**接口地址**: `GET /api/v1/questions`

**接口描述**: 获取题目列表

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |
| quest_type | string | 否 | 题目类型 |
| is_verified | bool | 否 | 是否已验证 |
| keyword | string | 否 | 关键词搜索 |

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "hash_key": "abc123...",
        "quest_type": "单选题",
        "quest_content": "题目内容...",
        "quest_options": {
          "A": "选项A",
          "B": "选项B",
          "C": "选项C",
          "D": "选项D"
        },
        "answer": {
          "A": "选项A"
        },
        "analysis": "答案解析...",
        "is_verified": true,
        "created_at": "2023-12-21T10:30:45Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20,
    "total_pages": 5
  }
}
```

---

### 10. 创建题目

**接口地址**: `POST /api/v1/questions`

**接口描述**: 创建新的题目

**请求参数**:
```json
{
  "quest_type": "单选题",
  "quest_content": "题目内容",
  "quest_options": {
    "A": "选项A",
    "B": "选项B",
    "C": "选项C",
    "D": "选项D"
  },
  "answer": {
    "A": "选项A"
  },
  "analysis": "答案解析",
  "image_url": "http://example.com/image.jpg"
}
```

**响应示例**: 返回创建的题目信息

---

### 11. 更新题目

**接口地址**: `PUT /api/v1/questions/{id}`

**接口描述**: 更新指定题目的信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 题目ID |

**请求参数**: 同创建题目，所有字段可选

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "message": "题目更新成功"
  }
}
```

---

### 12. 删除题目

**接口地址**: `DELETE /api/v1/questions/{id}`

**接口描述**: 软删除指定的题目

**路径参数**: 同更新题目

**响应示例**: 同更新题目

---

### 13. 搜索题目

**接口地址**: `GET /api/v1/questions/search`

**接口描述**: 根据关键词搜索题目

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| quest_type | string | 否 | 题目类型 |
| is_verified | bool | 否 | 是否已验证 |
| page | int | 否 | 页码 |
| page_size | int | 否 | 每页数量 |

**响应示例**: 同获取题目列表

## 使用示例

### cURL示例

```bash
# 图片解题
curl -X POST http://localhost:8080/api/v1/solve \
  -H "Content-Type: application/json" \
  -d '{"image_url":"http://img.igmdns.com/img/ca0001.jpg"}'

# 获取题目列表
curl "http://localhost:8080/api/v1/questions?page=1&page_size=10"

# 创建题目
curl -X POST http://localhost:8080/api/v1/questions \
  -H "Content-Type: application/json" \
  -d '{
    "quest_type": "单选题",
    "quest_content": "测试题目",
    "quest_options": {"A":"选项A","B":"选项B","C":"选项C","D":"选项D"},
    "answer": {"A":"选项A"},
    "analysis": "这是答案解析"
  }'
```

### JavaScript示例

```javascript
// 图片解题
const response = await fetch('/api/v1/solve', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    image_url: 'http://example.com/image.jpg'
  })
});

const result = await response.json();
console.log(result);
```

## 注意事项

1. 所有时间字段均为UTC时间
2. 图片URL必须可公开访问
3. 支持的图片格式：JPG、PNG、GIF、BMP、WEBP
4. 单次请求超时时间为60秒
5. 建议对API进行适当的限流控制
