# Qwen-Solve API 文档

本目录包含 Qwen-Solve API 的完整文档。

## 📚 文档列表

### API 文档
- **[api.md](./api.md)** - 完整的 API 接口文档
- **[api-quick-reference.md](./api-quick-reference.md)** - API 快速参考手册
- **[frontend-api-guide.md](./frontend-api-guide.md)** - 前端开发者 API 使用指南

### 题库管理文档 🆕
- **[question-field-requirements.md](./question-field-requirements.md)** - 题库字段详细要求文档
- **[question-field-quick-reference.md](./question-field-quick-reference.md)** - 题库字段快速参考卡片
- **[database-migration-guide.md](./database-migration-guide.md)** - 数据库迁移指南

### 系统文档
- **[configuration_summary.md](./configuration_summary.md)** - 配置说明文档
- **[deployment.md](./deployment.md)** - 部署指南

## 🚀 快速开始

### 前端开发者 - 题库管理
1. **快速上手**：查看 [question-field-quick-reference.md](./question-field-quick-reference.md) 快速了解字段要求
2. **详细开发**：阅读 [question-field-requirements.md](./question-field-requirements.md) 获取完整的字段规范和验证规则
3. **API集成**：参考 [frontend-api-guide.md](./frontend-api-guide.md) 进行API调用

### API开发者
1. **快速参考**：查看 [api-quick-reference.md](./api-quick-reference.md) 了解基本用法
2. **详细文档**：参考 [api.md](./api.md) 获取完整的接口说明

## 🎯 题库管理开发重点

### 字段验证要点
- **题目类型**：只支持`单选题`、`多选题`、`判断题`
- **选项格式**：单选/多选题用A-D，判断题用Y-N或A-B
- **答案一致性**：答案内容必须与选项内容完全一致
- **数量限制**：单选题1个答案，多选题1-3个答案，判断题1个答案

### 开发建议
- 使用提供的JavaScript验证函数
- 实现动态表单根据题型调整
- 添加实时验证和错误提示
- 确保答案与选项内容自动同步

## 📋 文档使用指南

| 需求场景 | 推荐文档 | 说明 |
|----------|----------|------|
| 快速了解题库字段 | [question-field-quick-reference.md](./question-field-quick-reference.md) | 简洁的参考卡片 |
| 开发题库表单 | [question-field-requirements.md](./question-field-requirements.md) | 详细的字段规范 |
| API接口调用 | [frontend-api-guide.md](./frontend-api-guide.md) | 前端API使用指南 |
| 快速测试API | [api-quick-reference.md](./api-quick-reference.md) | API快速参考 |
| 完整API文档 | [api.md](./api.md) | 详细的接口文档 |

## 📝 更新日志

- **2025-01-14**: 修正题库管理API业务逻辑 🔧
  - 移除hash_key字段的唯一性约束，允许相同缓存键存入
  - 更新数据库结构：`UNIQUE KEY uk_hash_key` → `KEY idx_hash_key`
  - 添加数据库迁移脚本和测试脚本
  - 更新相关文档说明
- **2025-01-14**: 新增题库管理字段要求文档
  - 添加 `question-field-requirements.md` 详细字段规范
  - 添加 `question-field-quick-reference.md` 快速参考卡片
  - 包含完整的验证规则和示例代码
- **2025-01-14**: 初始版本发布

## 🤝 贡献指南

如果您发现文档中的错误或需要补充内容，请：
1. 提交Issue描述问题
2. 或直接提交Pull Request
3. 确保新增内容符合现有文档风格

---
**维护者**: 开发团队  
**最后更新**: 2025-01-14
