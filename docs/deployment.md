# Qwen Solve API 部署指南

本文档详细介绍了如何部署Qwen Solve API服务。

## 环境要求

### 系统要求
- 操作系统: Linux/macOS/Windows
- 内存: 最少2GB，推荐4GB+
- 磁盘: 最少10GB可用空间
- 网络: 需要访问外网（调用Qwen API）

### 软件依赖
- Go 1.21+ (开发环境)
- MySQL 8.0+
- Redis 6.0+

## 部署步骤

### 1. 准备环境

#### 安装MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# macOS
brew install mysql
```

#### 安装Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

### 2. 数据库初始化

#### 创建数据库
```sql
CREATE DATABASE t_solve_go_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 导入表结构
```bash
mysql -h<host> -P<port> -u<username> -p<password> t_solve_go_api < database_design.sql
```

### 3. 配置文件

#### 创建环境变量文件
```bash
cp .env.example .env
```

#### 编辑配置
```bash
# 编辑.env文件，配置以下关键参数：

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release

# 数据库配置
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USERNAME=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=t_solve_go_api

# Redis配置
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Qwen API配置
QWEN_KEY=your_qwen_api_key
```

### 4. 构建应用

#### 从源码构建
```bash
# 克隆代码
git clone <repository-url>
cd qwen-solve

# 安装依赖
make deps

# 构建应用
make build
```

#### 使用预编译二进制
```bash
# 下载对应平台的二进制文件
wget <binary-download-url>
chmod +x qwen-solve-server
```

### 5. 启动服务

#### 直接启动
```bash
./build/qwen-solve-server
```

#### 使用systemd (推荐)
创建服务文件 `/etc/systemd/system/qwen-solve.service`:
```ini
[Unit]
Description=Qwen Solve API Server
After=network.target mysql.service redis.service

[Service]
Type=simple
User=qwen-solve
WorkingDirectory=/opt/qwen-solve
ExecStart=/opt/qwen-solve/qwen-solve-server
Restart=always
RestartSec=5
Environment=GIN_MODE=release

[Install]
WantedBy=multi-user.target
```

启动服务:
```bash
sudo systemctl daemon-reload
sudo systemctl enable qwen-solve
sudo systemctl start qwen-solve
```

#### 使用Docker
```bash
# 构建镜像
docker build -t qwen-solve:latest .

# 运行容器
docker run -d \
  --name qwen-solve \
  -p 8080:8080 \
  --env-file .env \
  qwen-solve:latest
```

### 6. 反向代理配置

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    
    ProxyPreserveHost On
    ProxyRequests Off
    ProxyPass / http://127.0.0.1:8080/
    ProxyPassReverse / http://127.0.0.1:8080/
</VirtualHost>
```

## 监控和维护

### 健康检查
```bash
curl http://localhost:8080/health
```

### 日志查看
```bash
# systemd服务日志
sudo journalctl -u qwen-solve -f

# Docker容器日志
docker logs -f qwen-solve
```

### 性能监控
建议使用以下工具进行监控：
- Prometheus + Grafana
- ELK Stack (Elasticsearch + Logstash + Kibana)
- 云服务监控 (AWS CloudWatch, 阿里云监控等)

### 备份策略
1. **数据库备份**
```bash
mysqldump -h<host> -P<port> -u<username> -p<password> t_solve_go_api > backup_$(date +%Y%m%d_%H%M%S).sql
```

2. **Redis备份**
```bash
redis-cli --rdb dump.rdb
```

3. **配置文件备份**
```bash
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz .env configs/
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用: `netstat -tlnp | grep 8080`
   - 检查配置文件是否正确
   - 查看错误日志

2. **数据库连接失败**
   - 验证数据库服务是否运行
   - 检查连接参数
   - 确认防火墙设置

3. **Redis连接失败**
   - 验证Redis服务是否运行
   - 检查密码和端口配置
   - 确认网络连通性

4. **Qwen API调用失败**
   - 验证API密钥是否有效
   - 检查网络连接
   - 确认API配额

### 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 优化查询语句
   - 配置连接池

2. **Redis优化**
   - 设置合适的内存限制
   - 配置持久化策略
   - 监控内存使用

3. **应用优化**
   - 调整并发参数
   - 优化缓存策略
   - 监控资源使用

## 安全建议

1. **网络安全**
   - 使用HTTPS
   - 配置防火墙
   - 限制访问IP

2. **数据安全**
   - 定期备份数据
   - 加密敏感信息
   - 设置强密码

3. **应用安全**
   - 定期更新依赖
   - 监控异常访问
   - 实施访问控制

## 扩展部署

### 负载均衡
使用Nginx或HAProxy实现负载均衡:
```nginx
upstream qwen_solve_backend {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    location / {
        proxy_pass http://qwen_solve_backend;
    }
}
```

### 高可用部署
- 数据库主从复制
- Redis集群
- 多实例部署
- 自动故障转移

## 联系支持

如遇到部署问题，请通过以下方式获取支持：
- 查看项目文档
- 提交Issue
- 联系技术支持
