# 题库管理字段要求文档

## 📋 概述

本文档详细描述了题库管理系统中新增和编辑题目时各字段的数据格式要求，包括字段类型、验证规则、示例数据等。

## 🏗️ 数据结构总览

### 创建题目请求结构
```typescript
interface CreateQuestionRequest {
  quest_type: QuestionType;      // 题目类型（必填）
  quest_content: string;         // 题目内容（必填）
  quest_options: QuestionOptions; // 题目选项（必填）
  answer: QuestionOptions;       // 正确答案（必填）
  analysis: string;              // 答案解析（必填）
  hash_key: string;              // 缓存键（必填）
  image_url?: string;            // 图片URL（可选）
}
```

### 更新题目请求结构
```typescript
interface UpdateQuestionRequest {
  quest_type?: QuestionType;      // 题目类型（可选）
  quest_content?: string;         // 题目内容（可选）
  quest_options?: QuestionOptions; // 题目选项（可选）
  answer?: QuestionOptions;       // 正确答案（可选）
  analysis?: string;              // 答案解析（可选）
  hash_key?: string;              // 缓存键（可选）
  image_url?: string;             // 图片URL（可选）
  is_verified?: boolean;          // 是否已验证（可选）
}
```

## 📝 字段详细要求

### 重要说明：缓存键（hash_key）
- **缓存键由前端提交**：前端需要提供hash_key字段
- **允许重复**：相同的hash_key可以重复存入数据库
- **无格式限制**：后端不对hash_key进行任何格式验证，前端提交什么就保存什么
- **特殊场景支持**：管理员可以手动指定任意的hash_key来处理业务需求
- **数据库结构**：已移除hash_key的唯一性约束，支持重复值存储

### 1. hash_key（缓存键）

**数据类型**: `string`
**是否必填**: 是（创建时）
**格式要求**: 32位MD5哈希值（小写十六进制字符串）

**验证规则**:
- 必须是32位长度的字符串
- 只能包含小写字母a-f和数字0-9
- 格式：`^[a-f0-9]{32}$`
- 允许重复值

**生成建议**:
```javascript
// 方式1：基于题目内容生成
function generateHashKey(questType, questContent, questOptions) {
  const content = questType + questContent + JSON.stringify(questOptions);
  return md5(content.toLowerCase().replace(/[\s\p{P}]/g, ''));
}

// 方式2：使用随机UUID转MD5
function generateRandomHashKey() {
  const uuid = generateUUID();
  return md5(uuid);
}

// 方式3：管理员手动指定（特殊场景）
const customHashKey = "abc123def456789012345678901234ab";
```

**示例**:
```json
{
  "hash_key": "abc123def456789012345678901234ab"
}
```

### 2. quest_type（题目类型）

**数据类型**: `string`  
**是否必填**: 是（创建时）  
**可选值**: 
- `"单选题"`
- `"多选题"`
- `"判断题"`

**验证规则**:
- 必须是上述三个值之一
- 不能为空或null

**示例**:
```json
{
  "quest_type": "单选题"
}
```

### 2. quest_content（题目内容）

**数据类型**: `string`  
**是否必填**: 是（创建时）  
**长度限制**: 不超过65535字符（TEXT类型）  

**验证规则**:
- 不能为空字符串
- 建议去除前后空格
- 支持换行符和特殊字符

**示例**:
```json
{
  "quest_content": "在城市道路上行驶时，遇到前方有行人横穿马路，驾驶员应该如何处理？"
}
```

### 3. quest_options（题目选项）

**数据类型**: `object`  
**是否必填**: 是（创建时）  

#### 单选题/多选题选项格式
**必须包含**: A、B、C、D 四个选项  
**键名**: 固定为 "A"、"B"、"C"、"D"  
**键值**: 选项内容字符串  

**示例**:
```json
{
  "quest_options": {
    "A": "立即停车让行",
    "B": "鸣喇叭警示",
    "C": "加速通过",
    "D": "减速慢行"
  }
}
```

#### 判断题选项格式
**必须包含**: Y、N 两个选项（或A、B两个选项）  
**键名**: "Y"和"N" 或 "A"和"B"  
**键值**: 选项内容字符串  

**示例**:
```json
{
  "quest_options": {
    "Y": "正确",
    "N": "错误"
  }
}
```

**或者**:
```json
{
  "quest_options": {
    "A": "正确",
    "B": "错误"
  }
}
```

### 4. answer（正确答案）

**数据类型**: `object`  
**是否必填**: 是（创建时）  

#### 单选题答案格式
**包含**: 一个正确选项  
**格式**: 键为选项字母，值为对应的选项内容  

**示例**:
```json
{
  "answer": {
    "A": "立即停车让行"
  }
}
```

#### 多选题答案格式
**包含**: 一个或多个正确选项  
**格式**: 键为选项字母，值为对应的选项内容  

**示例**:
```json
{
  "answer": {
    "A": "加大跟车距离,降低行驶速度",
    "B": "提前开启雾灯、危险报警闪光灯",
    "C": "以前车尾灯作为判断安全距离的参照物"
  }
}
```

#### 判断题答案格式
**包含**: 一个正确选项（Y或N，或A或B）  

**示例**:
```json
{
  "answer": {
    "Y": "正确"
  }
}
```

**或者**:
```json
{
  "answer": {
    "A": "正确"
  }
}
```

### 5. analysis（答案解析）

**数据类型**: `string`  
**是否必填**: 是（创建时）  
**长度限制**: 不超过65535字符（TEXT类型）  

**验证规则**:
- 不能为空字符串
- 建议提供详细的解析说明
- 支持换行符和特殊字符

**示例**:
```json
{
  "analysis": "根据《道路交通安全法》规定，机动车行经人行横道时，应当减速行驶；遇行人正在通过人行横道，应当停车让行。选项A正确。"
}
```

### 6. image_url（图片URL）

**数据类型**: `string`  
**是否必填**: 否  
**长度限制**: 不超过500字符  

**验证规则**:
- 必须是有效的HTTP或HTTPS URL
- 图片必须可公开访问
- 支持的格式：JPG、PNG、GIF、BMP、WEBP

**示例**:
```json
{
  "image_url": "https://example.com/images/traffic-question-001.jpg"
}
```

### 7. is_verified（是否已验证）

**数据类型**: `boolean`  
**是否必填**: 否（仅更新时可用）  
**默认值**: `false`  

**可选值**:
- `true`: 已验证
- `false`: 未验证

**示例**:
```json
{
  "is_verified": true
}
```

## 📋 完整示例

### 单选题示例
```json
{
  "quest_type": "单选题",
  "quest_content": "在城市道路上行驶时，遇到前方有行人横穿马路，驾驶员应该如何处理？",
  "quest_options": {
    "A": "立即停车让行",
    "B": "鸣喇叭警示",
    "C": "加速通过",
    "D": "减速慢行"
  },
  "answer": {
    "A": "立即停车让行"
  },
  "analysis": "根据《道路交通安全法》规定，机动车行经人行横道时，应当减速行驶；遇行人正在通过人行横道，应当停车让行。选项A正确。",
  "image_url": "https://example.com/images/traffic-question-001.jpg"
}
```

### 多选题示例
```json
{
  "quest_type": "多选题",
  "quest_content": "雾天跟车行驶，应如何安全驾驶？",
  "quest_options": {
    "A": "加大跟车距离，降低行驶速度",
    "B": "提前开启雾灯、危险报警闪光灯",
    "C": "以前车尾灯作为判断安全距离的参照物",
    "D": "按喇叭提示行车位置"
  },
  "answer": {
    "A": "加大跟车距离，降低行驶速度",
    "B": "提前开启雾灯、危险报警闪光灯",
    "C": "以前车尾灯作为判断安全距离的参照物"
  },
  "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。",
  "image_url": "https://example.com/images/fog-driving.jpg"
}
```

### 判断题示例
```json
{
  "quest_type": "判断题",
  "quest_content": "机动车在高速公路上发生故障时，可以在车行道内停车修理。",
  "quest_options": {
    "Y": "正确",
    "N": "错误"
  },
  "answer": {
    "N": "错误"
  },
  "analysis": "根据《道路交通安全法》规定，机动车在高速公路上发生故障时，应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放；难以移动的，应当持续开启危险报警闪光灯，并在来车方向设置警告标志等措施扩大示警距离。不能在车行道内停车修理。",
  "image_url": "https://example.com/images/highway-breakdown.jpg"
}
```

## ⚠️ 验证规则和约束

### 数据一致性验证

1. **选项与答案一致性**
   - 答案中的键必须存在于选项中
   - 答案中的值必须与选项中对应键的值完全一致

2. **题型与选项数量一致性**
   - 单选题/多选题：必须有且仅有A、B、C、D四个选项
   - 判断题：必须有且仅有Y、N两个选项（或A、B两个选项）

3. **题型与答案数量一致性**
   - 单选题：答案必须有且仅有1个选项
   - 多选题：答案必须有1个或多个选项（但不能是全部4个选项）
   - 判断题：答案必须有且仅有1个选项

### 字段长度限制

| 字段名 | 最大长度 | 数据库类型 |
|--------|----------|------------|
| quest_content | 65,535字符 | TEXT |
| analysis | 65,535字符 | TEXT |
| image_url | 500字符 | VARCHAR(500) |

### JSON格式要求

- `quest_options` 和 `answer` 字段必须是有效的JSON对象
- 键名必须是字符串类型
- 键值必须是字符串类型
- 不允许嵌套对象或数组

## ❌ 常见错误示例

### 1. 题型与选项不匹配
```json
// ❌ 错误：判断题使用了4个选项
{
  "quest_type": "判断题",
  "quest_options": {
    "A": "选项A",
    "B": "选项B",
    "C": "选项C",
    "D": "选项D"
  }
}

// ✅ 正确：判断题使用2个选项
{
  "quest_type": "判断题",
  "quest_options": {
    "Y": "正确",
    "N": "错误"
  }
}
```

### 2. 答案与选项不一致
```json
// ❌ 错误：答案内容与选项内容不匹配
{
  "quest_options": {
    "A": "立即停车让行"
  },
  "answer": {
    "A": "停车让行"  // 内容不完全一致
  }
}

// ✅ 正确：答案内容与选项内容完全一致
{
  "quest_options": {
    "A": "立即停车让行"
  },
  "answer": {
    "A": "立即停车让行"
  }
}
```

### 3. 单选题多个答案
```json
// ❌ 错误：单选题不能有多个答案
{
  "quest_type": "单选题",
  "answer": {
    "A": "选项A",
    "B": "选项B"
  }
}

// ✅ 正确：单选题只能有一个答案
{
  "quest_type": "单选题",
  "answer": {
    "A": "选项A"
  }
}
```

## 🔧 前端表单验证建议

### JavaScript验证函数示例

```javascript
// 验证题目类型
function validateQuestionType(questType) {
  const validTypes = ['单选题', '多选题', '判断题'];
  return validTypes.includes(questType);
}

// 验证选项格式
function validateQuestionOptions(questType, options) {
  if (!options || typeof options !== 'object') {
    return { valid: false, message: '选项必须是对象格式' };
  }

  const keys = Object.keys(options);

  if (questType === '判断题') {
    // 判断题验证
    if (keys.length !== 2) {
      return { valid: false, message: '判断题必须有且仅有2个选项' };
    }
    const validKeys = ['Y', 'N'] || ['A', 'B'];
    if (!keys.every(key => validKeys.includes(key))) {
      return { valid: false, message: '判断题选项键名必须是Y、N或A、B' };
    }
  } else {
    // 单选题/多选题验证
    if (keys.length !== 4) {
      return { valid: false, message: '单选题/多选题必须有且仅有4个选项' };
    }
    const requiredKeys = ['A', 'B', 'C', 'D'];
    if (!requiredKeys.every(key => keys.includes(key))) {
      return { valid: false, message: '单选题/多选题选项键名必须是A、B、C、D' };
    }
  }

  // 验证选项内容不为空
  for (const [key, value] of Object.entries(options)) {
    if (!value || typeof value !== 'string' || value.trim() === '') {
      return { valid: false, message: `选项${key}的内容不能为空` };
    }
  }

  return { valid: true };
}

// 验证答案格式
function validateAnswer(questType, options, answer) {
  if (!answer || typeof answer !== 'object') {
    return { valid: false, message: '答案必须是对象格式' };
  }

  const answerKeys = Object.keys(answer);
  const optionKeys = Object.keys(options);

  // 验证答案键是否存在于选项中
  for (const key of answerKeys) {
    if (!optionKeys.includes(key)) {
      return { valid: false, message: `答案选项${key}在题目选项中不存在` };
    }
  }

  // 验证答案内容与选项内容是否一致
  for (const [key, value] of Object.entries(answer)) {
    if (options[key] !== value) {
      return { valid: false, message: `答案选项${key}的内容与题目选项不一致` };
    }
  }

  // 验证答案数量
  if (questType === '单选题' || questType === '判断题') {
    if (answerKeys.length !== 1) {
      return { valid: false, message: `${questType}必须有且仅有1个答案` };
    }
  } else if (questType === '多选题') {
    if (answerKeys.length < 1 || answerKeys.length >= 4) {
      return { valid: false, message: '多选题答案数量必须在1-3个之间' };
    }
  }

  return { valid: true };
}

// 完整表单验证
function validateQuestionForm(formData) {
  const errors = [];

  // 验证必填字段
  if (!formData.quest_type) {
    errors.push('题目类型不能为空');
  } else if (!validateQuestionType(formData.quest_type)) {
    errors.push('题目类型无效');
  }

  if (!formData.quest_content || formData.quest_content.trim() === '') {
    errors.push('题目内容不能为空');
  }

  if (!formData.analysis || formData.analysis.trim() === '') {
    errors.push('答案解析不能为空');
  }

  // 验证选项
  const optionsValidation = validateQuestionOptions(formData.quest_type, formData.quest_options);
  if (!optionsValidation.valid) {
    errors.push(optionsValidation.message);
  }

  // 验证答案
  if (optionsValidation.valid) {
    const answerValidation = validateAnswer(formData.quest_type, formData.quest_options, formData.answer);
    if (!answerValidation.valid) {
      errors.push(answerValidation.message);
    }
  }

  // 验证图片URL（如果提供）
  if (formData.image_url && formData.image_url.trim() !== '') {
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(formData.image_url)) {
      errors.push('图片URL格式无效，必须以http://或https://开头');
    }
  }

  return {
    valid: errors.length === 0,
    errors: errors
  };
}
```

## 📡 API响应格式

### 成功响应
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "hash_key": "abc123def456",
    "quest_type": "单选题",
    "quest_content": "题目内容",
    "quest_options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C",
      "D": "选项D"
    },
    "answer": {
      "A": "选项A"
    },
    "analysis": "答案解析",
    "user_image": null,
    "image_url": "https://example.com/image.jpg",
    "is_verified": false,
    "created_at": "2025-01-14T10:00:00Z",
    "updated_at": "2025-01-14T10:00:00Z",
    "deleted_at": null
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

### 验证错误响应
```json
{
  "code": 422,
  "message": "数据验证失败",
  "data": {
    "errors": [
      {
        "field": "quest_type",
        "message": "题目类型不能为空"
      },
      {
        "field": "quest_options",
        "message": "单选题必须有4个选项"
      }
    ]
  }
}
```

## 📋 前端开发清单

### 必须实现的验证
- [ ] 题目类型选择验证
- [ ] 题目内容非空验证
- [ ] 选项数量和格式验证
- [ ] 答案与选项一致性验证
- [ ] 答案数量验证（单选1个，多选1-3个，判断1个）
- [ ] 解析内容非空验证
- [ ] 图片URL格式验证（可选）

### 用户体验优化
- [ ] 根据题型动态显示选项输入框
- [ ] 实时验证并显示错误提示
- [ ] 答案选择与选项内容自动同步
- [ ] 表单提交前完整性检查
- [ ] 加载状态和错误状态处理

### 建议的UI组件
- 题型选择：单选按钮组或下拉选择
- 题目内容：多行文本框
- 选项输入：根据题型动态显示的输入框组
- 答案选择：复选框（多选题）或单选框（单选题、判断题）
- 解析输入：多行文本框
- 图片上传：文件上传组件或URL输入框

## 🔍 调试和测试建议

1. **使用浏览器开发者工具**检查API请求和响应
2. **创建测试用例**覆盖各种题型和边界情况
3. **验证JSON格式**确保数据结构正确
4. **测试网络异常**处理API调用失败的情况
5. **检查数据一致性**确保前端显示与后端存储一致

---

**文档版本**: v1.0
**最后更新**: 2025-01-14
**维护者**: 开发团队
