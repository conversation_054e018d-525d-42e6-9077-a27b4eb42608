# 题库字段快速参考卡片

## 🚀 字段总览

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `hash_key` | string | ✅ | 缓存键，32位MD5哈希值 |
| `quest_type` | string | ✅ | 题目类型：`单选题`/`多选题`/`判断题` |
| `quest_content` | string | ✅ | 题目内容，最大65535字符 |
| `quest_options` | object | ✅ | 题目选项JSON对象 |
| `answer` | object | ✅ | 正确答案JSON对象 |
| `analysis` | string | ✅ | 答案解析，最大65535字符 |
| `image_url` | string | ❌ | 图片URL，最大500字符 |
| `is_verified` | boolean | ❌ | 是否已验证（仅更新时） |

> **📌 重要提示**：缓存键（hash_key）由前端提交，允许重复，格式：32位小写MD5哈希值

## 📝 选项格式规则

### 单选题/多选题
```json
{
  "quest_options": {
    "A": "选项A内容",
    "B": "选项B内容", 
    "C": "选项C内容",
    "D": "选项D内容"
  }
}
```
- **必须有4个选项**：A、B、C、D
- **键名固定**：不能使用其他字母

### 判断题
```json
{
  "quest_options": {
    "Y": "正确",
    "N": "错误"
  }
}
```
**或者**
```json
{
  "quest_options": {
    "A": "正确",
    "B": "错误"
  }
}
```
- **必须有2个选项**：Y/N 或 A/B
- **内容通常是**：正确/错误

## ✅ 答案格式规则

### 单选题答案
```json
{
  "answer": {
    "A": "选项A内容"  // 只能有1个答案
  }
}
```

### 多选题答案
```json
{
  "answer": {
    "A": "选项A内容",
    "C": "选项C内容"  // 可以有1-3个答案
  }
}
```

### 判断题答案
```json
{
  "answer": {
    "Y": "正确"  // 只能有1个答案
  }
}
```

## ⚡ 验证要点

### 🔴 必须验证
1. **答案键存在于选项中**
2. **答案值与选项值完全一致**
3. **题型与选项数量匹配**
4. **题型与答案数量匹配**

### 📊 数量规则
| 题型 | 选项数量 | 答案数量 |
|------|----------|----------|
| 单选题 | 4个(A-D) | 1个 |
| 多选题 | 4个(A-D) | 1-3个 |
| 判断题 | 2个(Y/N或A/B) | 1个 |

## 🚨 常见错误

### ❌ 错误示例
```json
// 错误1：判断题选项数量错误
{
  "quest_type": "判断题",
  "quest_options": {"A":"选项A","B":"选项B","C":"选项C","D":"选项D"}
}

// 错误2：答案内容不匹配
{
  "quest_options": {"A": "立即停车让行"},
  "answer": {"A": "停车让行"}  // 内容不一致
}

// 错误3：单选题多个答案
{
  "quest_type": "单选题", 
  "answer": {"A": "选项A", "B": "选项B"}  // 单选不能多答案
}
```

## 💡 前端实现提示

### JavaScript验证模板
```javascript
// 基础验证函数
function validateQuestion(data) {
  const errors = [];
  
  // 验证题型
  if (!['单选题','多选题','判断题'].includes(data.quest_type)) {
    errors.push('题目类型无效');
  }
  
  // 验证选项数量
  const optionCount = Object.keys(data.quest_options || {}).length;
  if (data.quest_type === '判断题' && optionCount !== 2) {
    errors.push('判断题必须有2个选项');
  } else if (['单选题','多选题'].includes(data.quest_type) && optionCount !== 4) {
    errors.push('单选题/多选题必须有4个选项');
  }
  
  // 验证答案数量
  const answerCount = Object.keys(data.answer || {}).length;
  if (data.quest_type === '单选题' && answerCount !== 1) {
    errors.push('单选题只能有1个答案');
  } else if (data.quest_type === '多选题' && (answerCount < 1 || answerCount > 3)) {
    errors.push('多选题答案数量必须在1-3个之间');
  } else if (data.quest_type === '判断题' && answerCount !== 1) {
    errors.push('判断题只能有1个答案');
  }
  
  return { valid: errors.length === 0, errors };
}
```

### Vue.js表单示例
```vue
<template>
  <el-form :model="form" :rules="rules" ref="questionForm">
    <!-- 题型选择 -->
    <el-form-item label="题目类型" prop="quest_type">
      <el-radio-group v-model="form.quest_type" @change="onTypeChange">
        <el-radio label="单选题">单选题</el-radio>
        <el-radio label="多选题">多选题</el-radio>
        <el-radio label="判断题">判断题</el-radio>
      </el-radio-group>
    </el-form-item>
    
    <!-- 动态选项输入 -->
    <el-form-item v-if="form.quest_type !== '判断题'" label="选项A" prop="quest_options.A">
      <el-input v-model="form.quest_options.A" @input="syncAnswer('A')"></el-input>
    </el-form-item>
    <!-- 更多选项... -->
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        quest_type: '单选题',
        quest_content: '',
        quest_options: { A: '', B: '', C: '', D: '' },
        answer: {},
        analysis: ''
      }
    }
  },
  methods: {
    onTypeChange(type) {
      // 根据题型调整选项结构
      if (type === '判断题') {
        this.form.quest_options = { Y: '正确', N: '错误' };
      } else {
        this.form.quest_options = { A: '', B: '', C: '', D: '' };
      }
      this.form.answer = {};
    },
    
    syncAnswer(key) {
      // 自动同步答案内容
      if (this.form.answer[key]) {
        this.form.answer[key] = this.form.quest_options[key];
      }
    }
  }
}
</script>
```

## 📋 开发检查清单

- [ ] 题型选择器实现
- [ ] 动态选项输入框
- [ ] 答案选择与选项同步
- [ ] 实时表单验证
- [ ] 错误提示显示
- [ ] API调用错误处理
- [ ] 数据格式验证
- [ ] 提交前最终检查

---
**快速参考** | 详细文档：[question-field-requirements.md](./question-field-requirements.md)
