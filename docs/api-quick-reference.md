# Qwen-Solve API 快速参考

## 🔗 基础信息
- **服务地址**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **认证**: 暂无需要

## 📚 题库管理 API

### 获取题目列表
```http
GET /api/v1/questions?page=1&page_size=20&quest_type=单选题&is_verified=true&keyword=驾驶
```

### 获取单个题目
```http
GET /api/v1/questions/{id}
```

### 创建题目
```http
POST /api/v1/questions
Content-Type: application/json

{
  "quest_type": "单选题",
  "quest_content": "题目内容",
  "quest_options": {
    "A": "选项A",
    "B": "选项B", 
    "C": "选项C",
    "D": "选项D"
  },
  "answer": {
    "A": "选项A"
  },
  "analysis": "答案解析"
}
```

### 更新题目
```http
PUT /api/v1/questions/{id}
Content-Type: application/json

{
  "quest_content": "更新后的题目内容",
  "is_verified": true
}
```

### 删除题目
```http
DELETE /api/v1/questions/{id}
```

### 搜索题目
```http
GET /api/v1/questions/search?keyword=驾驶&quest_type=单选题&page=1&page_size=20
```

## 📊 请求日志 API

### 获取日志列表
```http
GET /api/v1/logs?page=1&page_size=20&status=success
```

### 获取单个日志
```http
GET /api/v1/logs/{id}
```

### 删除日志
```http
DELETE /api/v1/logs/{id}
```

### 获取日志统计
```http
GET /api/v1/logs/statistics
```

### 获取错误日志
```http
GET /api/v1/logs/errors?limit=50&page=1&page_size=20
```

### 获取最近日志
```http
GET /api/v1/logs/recent?limit=10
```

## 🏷️ 数据格式

### 响应格式
```json
{
  "code": 0,
  "message": "成功",
  "data": {},
  "trace_id": ""
}
```

### 题目对象
```json
{
  "id": 1,
  "hash_key": "abc123",
  "quest_type": "单选题",
  "quest_content": "题目内容",
  "quest_options": {
    "A": "选项A",
    "B": "选项B",
    "C": "选项C", 
    "D": "选项D"
  },
  "answer": {
    "A": "选项A"
  },
  "analysis": "答案解析",
  "user_image": "http://example.com/image.jpg",
  "image_url": "http://example.com/standard.jpg",
  "is_verified": true,
  "created_at": "2025-06-14T10:00:00Z",
  "updated_at": "2025-06-14T10:00:00Z"
}
```

### 日志对象
```json
{
  "id": 1,
  "request_id": "req_1749867323001840000",
  "user_image_url": "http://img.igmdns.com/img/ca0001.jpg",
  "response_data": "[{\"quest_type\":\"单选题\"}]",
  "is_redis_hit": true,
  "is_mysql_hit": false,
  "error_message": null,
  "processing_time": 2043,
  "request_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0",
  "created_at": "2025-06-14T10:00:00Z",
  "status": "success"
}
```

### 分页响应
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [],
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

## ❌ 错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 内部服务器错误 |
| 1001 | 图片不存在 |
| 1004 | 数据解析失败 |
| 1005 | 数据库操作失败 |

## 💻 JavaScript 快速示例

```javascript
// 基础API类
class QwenAPI {
  constructor(baseURL = 'http://localhost:8080') {
    this.baseURL = baseURL;
  }

  async request(method, url, data) {
    const response = await fetch(`${this.baseURL}${url}`, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : null
    });
    
    const result = await response.json();
    if (result.code !== 0) throw new Error(result.message);
    return result.data;
  }

  // 题目管理
  getQuestions(params) {
    const query = new URLSearchParams(params).toString();
    return this.request('GET', `/api/v1/questions?${query}`);
  }

  createQuestion(data) {
    return this.request('POST', '/api/v1/questions', data);
  }

  updateQuestion(id, data) {
    return this.request('PUT', `/api/v1/questions/${id}`, data);
  }

  deleteQuestion(id) {
    return this.request('DELETE', `/api/v1/questions/${id}`);
  }

  // 日志管理
  getLogs(params) {
    const query = new URLSearchParams(params).toString();
    return this.request('GET', `/api/v1/logs?${query}`);
  }

  getLogStatistics() {
    return this.request('GET', '/api/v1/logs/statistics');
  }
}

// 使用示例
const api = new QwenAPI();

// 获取题目列表
const questions = await api.getQuestions({ page: 1, page_size: 20 });

// 创建题目
const newQuestion = await api.createQuestion({
  quest_type: '单选题',
  quest_content: '这是题目内容',
  quest_options: { A: '选项A', B: '选项B', C: '选项C', D: '选项D' },
  answer: { A: '选项A' },
  analysis: '答案解析'
});

// 获取统计信息
const stats = await api.getLogStatistics();
```

## 📝 注意事项

1. **题目类型**: 只支持 `单选题`、`多选题`、`判断题`
2. **选项格式**: 单选/多选题用A-D，判断题用Y-N或A-B
3. **分页**: 页码从1开始，最大每页100条
4. **时间格式**: ISO 8601格式
5. **错误处理**: 建议使用try-catch包装所有API调用

---
**快速参考版本**: v1.0 | **完整文档**: [frontend-api-guide.md](./frontend-api-guide.md)
