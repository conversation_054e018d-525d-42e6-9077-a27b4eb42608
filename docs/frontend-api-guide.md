# Qwen-Solve API 前端接入文档

## 📋 目录
- [基础信息](#基础信息)
- [认证方式](#认证方式)
- [题库管理 API](#题库管理-api)
- [请求日志 API](#请求日志-api)
- [错误处理](#错误处理)
- [示例代码](#示例代码)

## 🔧 基础信息

### 服务地址
- **开发环境**: `http://localhost:8080`
- **生产环境**: `待定`

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **请求方法**: `GET`, `POST`, `PUT`, `DELETE`

### 响应格式
```json
{
  "code": 0,           // 状态码：0=成功，非0=失败
  "message": "成功",    // 响应消息
  "data": {},          // 响应数据
  "trace_id": ""       // 请求追踪ID（可选）
}
```

## 🔐 认证方式

当前版本暂无认证要求，直接调用即可。

## 🚀 快速开始

### 1. 安装依赖（可选）
```bash
# 如果使用axios
npm install axios

# 如果使用Element Plus (Vue3)
npm install element-plus

# 如果使用Ant Design (React)
npm install antd
```

### 2. 基础配置
```javascript
// config/api.js
export const API_CONFIG = {
  BASE_URL: 'http://localhost:8080',
  TIMEOUT: 10000,
  HEADERS: {
    'Content-Type': 'application/json'
  }
};
```

### 3. 快速测试
```javascript
// 测试连接
fetch('http://localhost:8080/health')
  .then(response => response.json())
  .then(data => {
    console.log('服务连接正常:', data);
  })
  .catch(error => {
    console.error('服务连接失败:', error);
  });
```

## 📚 题库管理 API

### 1. 获取题目列表

**接口地址**: `GET /api/v1/questions`

**请求参数**:
```javascript
{
  page: 1,              // 页码，从1开始
  page_size: 20,        // 每页数量，最大100
  quest_type: "单选题",  // 题目类型：单选题/多选题/判断题（可选）
  is_verified: true,    // 是否已验证：true/false（可选）
  keyword: "驾驶"       // 关键词搜索（可选）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "hash_key": "abc123",
        "quest_type": "单选题",
        "quest_content": "这是题目内容",
        "quest_options": {
          "A": "选项A",
          "B": "选项B",
          "C": "选项C",
          "D": "选项D"
        },
        "answer": {
          "A": "选项A"
        },
        "analysis": "答案解析",
        "user_image": "http://example.com/image.jpg",
        "image_url": "http://example.com/standard.jpg",
        "is_verified": true,
        "created_at": "2025-06-14T10:00:00Z",
        "updated_at": "2025-06-14T10:00:00Z"
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

### 2. 获取单个题目

**接口地址**: `GET /api/v1/questions/{id}`

**路径参数**:
- `id`: 题目ID

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "hash_key": "abc123",
    "quest_type": "单选题",
    "quest_content": "这是题目内容",
    "quest_options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C",
      "D": "选项D"
    },
    "answer": {
      "A": "选项A"
    },
    "analysis": "答案解析",
    "user_image": "http://example.com/image.jpg",
    "image_url": "http://example.com/standard.jpg",
    "is_verified": true,
    "created_at": "2025-06-14T10:00:00Z",
    "updated_at": "2025-06-14T10:00:00Z"
  }
}
```

### 3. 创建题目

**接口地址**: `POST /api/v1/questions`

**请求体**:
```json
{
  "quest_type": "单选题",
  "quest_content": "这是题目内容",
  "quest_options": {
    "A": "选项A",
    "B": "选项B",
    "C": "选项C",
    "D": "选项D"
  },
  "answer": {
    "A": "选项A"
  },
  "analysis": "答案解析",
  "image_url": "http://example.com/image.jpg"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 1,
    "hash_key": "abc123",
    // ... 其他字段同获取题目接口
  }
}
```

### 4. 更新题目

**接口地址**: `PUT /api/v1/questions/{id}`

**路径参数**:
- `id`: 题目ID

**请求体**:
```json
{
  "quest_type": "多选题",
  "quest_content": "更新后的题目内容",
  "quest_options": {
    "A": "新选项A",
    "B": "新选项B",
    "C": "新选项C",
    "D": "新选项D"
  },
  "answer": {
    "A": "新选项A",
    "B": "新选项B"
  },
  "analysis": "更新后的解析",
  "image_url": "http://example.com/new-image.jpg",
  "is_verified": true
}
```

### 5. 删除题目

**接口地址**: `DELETE /api/v1/questions/{id}`

**路径参数**:
- `id`: 题目ID

**响应示例**:
```json
{
  "code": 0,
  "message": "删除成功"
}
```

### 6. 搜索题目

**接口地址**: `GET /api/v1/questions/search`

**请求参数**:
```javascript
{
  keyword: "驾驶",      // 搜索关键词
  quest_type: "单选题", // 题目类型（可选）
  is_verified: true,   // 是否已验证（可选）
  page: 1,             // 页码
  page_size: 20        // 每页数量
}
```

## 📊 请求日志 API

### 1. 获取请求日志列表

**接口地址**: `GET /api/v1/logs`

**请求参数**:
```javascript
{
  page: 1,              // 页码，从1开始
  page_size: 20,        // 每页数量，最大100
  status: "success",    // 状态：success/error（可选）
  start_time: "2025-06-14T00:00:00Z", // 开始时间（可选）
  end_time: "2025-06-14T23:59:59Z"    // 结束时间（可选）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "list": [
      {
        "id": 1,
        "request_id": "req_1749867323001840000",
        "user_image_url": "http://img.igmdns.com/img/ca0001.jpg",
        "response_data": "[{\"quest_type\":\"单选题\",\"quest_content\":\"题目内容\"}]",
        "is_redis_hit": true,
        "is_mysql_hit": false,
        "error_message": null,
        "processing_time": 2043,
        "request_ip": "127.0.0.1",
        "user_agent": "Mozilla/5.0",
        "created_at": "2025-06-14T10:00:00Z",
        "status": "success"
      }
    ],
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

### 2. 获取单个请求日志

**接口地址**: `GET /api/v1/logs/{id}`

**路径参数**:
- `id`: 日志ID

### 3. 删除请求日志

**接口地址**: `DELETE /api/v1/logs/{id}`

**路径参数**:
- `id`: 日志ID

### 4. 获取日志统计

**接口地址**: `GET /api/v1/logs/statistics`

**响应示例**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "total_requests": 1000,
    "success_requests": 850,
    "error_requests": 150,
    "success_rate": 85.0,
    "avg_processing_time": 3500,
    "redis_hit_rate": 60.5,
    "mysql_hit_rate": 25.3,
    "today_requests": 120,
    "today_success": 100,
    "today_errors": 20
  }
}
```

### 5. 获取错误日志

**接口地址**: `GET /api/v1/logs/errors`

**请求参数**:
```javascript
{
  limit: 50,           // 限制数量，默认20，最大100
  page: 1,             // 页码
  page_size: 20        // 每页数量
}
```

### 6. 获取最近日志

**接口地址**: `GET /api/v1/logs/recent`

**请求参数**:
```javascript
{
  limit: 10            // 限制数量，默认10，最大50
}
```

## ❌ 错误处理

### 错误码说明
```javascript
const ERROR_CODES = {
  0: "成功",
  400: "请求参数错误",
  401: "未授权",
  403: "禁止访问",
  404: "资源不存在",
  422: "数据验证失败",
  500: "内部服务器错误",
  1001: "图片不存在",
  1002: "图片格式不正确",
  1003: "AI服务调用失败",
  1004: "数据解析失败",
  1005: "数据库操作失败",
  1006: "缓存操作失败",
  1007: "题目类型识别失败",
  1008: "图片不标准"
};
```

### 错误响应示例
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "details": "quest_type字段不能为空"
  }
}
```

## 🏷️ TypeScript 类型定义

```typescript
// types/api.ts
export interface APIResponse<T = any> {
  code: number;
  message: string;
  data: T;
  trace_id?: string;
}

export interface PaginationParams {
  page: number;
  page_size: number;
}

export interface PaginationResponse<T> {
  list: T[];
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// 题目相关类型
export type QuestionType = '单选题' | '多选题' | '判断题';

export interface QuestionOptions {
  A?: string;
  B?: string;
  C?: string;
  D?: string;
  Y?: string;
  N?: string;
}

export interface Question {
  id: number;
  hash_key: string;
  quest_type: QuestionType;
  quest_content: string;
  quest_options: QuestionOptions;
  answer: QuestionOptions;
  analysis: string;
  user_image?: string;
  image_url?: string;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CreateQuestionRequest {
  quest_type: QuestionType;
  quest_content: string;
  quest_options: QuestionOptions;
  answer: QuestionOptions;
  analysis: string;
  image_url?: string;
}

export interface UpdateQuestionRequest {
  quest_type?: QuestionType;
  quest_content?: string;
  quest_options?: QuestionOptions;
  answer?: QuestionOptions;
  analysis?: string;
  image_url?: string;
  is_verified?: boolean;
}

export interface QuestionListParams extends PaginationParams {
  quest_type?: QuestionType;
  is_verified?: boolean;
  keyword?: string;
}

export interface QuestionSearchParams extends PaginationParams {
  keyword: string;
  quest_type?: QuestionType;
  is_verified?: boolean;
}

// 日志相关类型
export type LogStatus = 'success' | 'error';

export interface RequestLog {
  id: number;
  request_id: string;
  user_image_url: string;
  response_data?: string;
  is_redis_hit: boolean;
  is_mysql_hit: boolean;
  error_message?: string;
  processing_time: number;
  request_ip: string;
  user_agent: string;
  created_at: string;
  status: LogStatus;
}

export interface LogListParams extends PaginationParams {
  status?: LogStatus;
  start_time?: string;
  end_time?: string;
}

export interface LogStatistics {
  total_requests: number;
  success_requests: number;
  error_requests: number;
  success_rate: number;
  avg_processing_time: number;
  redis_hit_rate: number;
  mysql_hit_rate: number;
  today_requests: number;
  today_success: number;
  today_errors: number;
}

export interface ErrorLogParams extends PaginationParams {
  limit?: number;
}
```

## 💻 示例代码

### TypeScript API 客户端

```typescript
// api/client.ts
import type {
  APIResponse,
  Question,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionListParams,
  QuestionSearchParams,
  PaginationResponse,
  RequestLog,
  LogListParams,
  LogStatistics,
  ErrorLogParams
} from '@/types/api';

class QwenSolveAPI {
  private baseURL: string;

  constructor(baseURL = 'http://localhost:8080') {
    this.baseURL = baseURL;
  }

  private async request<T>(
    method: string,
    url: string,
    data?: any
  ): Promise<T> {
    const config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.baseURL}${url}`, config);
    const result: APIResponse<T> = await response.json();

    if (result.code !== 0) {
      throw new Error(result.message);
    }

    return result.data;
  }

  // 题库管理
  async getQuestions(params: QuestionListParams = { page: 1, page_size: 20 }): Promise<PaginationResponse<Question>> {
    const query = new URLSearchParams(params as any).toString();
    return this.request<PaginationResponse<Question>>('GET', `/api/v1/questions?${query}`);
  }

  async getQuestion(id: number): Promise<Question> {
    return this.request<Question>('GET', `/api/v1/questions/${id}`);
  }

  async createQuestion(data: CreateQuestionRequest): Promise<Question> {
    return this.request<Question>('POST', '/api/v1/questions', data);
  }

  async updateQuestion(id: number, data: UpdateQuestionRequest): Promise<Question> {
    return this.request<Question>('PUT', `/api/v1/questions/${id}`, data);
  }

  async deleteQuestion(id: number): Promise<void> {
    return this.request<void>('DELETE', `/api/v1/questions/${id}`);
  }

  async searchQuestions(params: QuestionSearchParams): Promise<PaginationResponse<Question>> {
    const query = new URLSearchParams(params as any).toString();
    return this.request<PaginationResponse<Question>>('GET', `/api/v1/questions/search?${query}`);
  }

  // 日志管理
  async getLogs(params: LogListParams = { page: 1, page_size: 20 }): Promise<PaginationResponse<RequestLog>> {
    const query = new URLSearchParams(params as any).toString();
    return this.request<PaginationResponse<RequestLog>>('GET', `/api/v1/logs?${query}`);
  }

  async getLog(id: number): Promise<RequestLog> {
    return this.request<RequestLog>('GET', `/api/v1/logs/${id}`);
  }

  async deleteLog(id: number): Promise<void> {
    return this.request<void>('DELETE', `/api/v1/logs/${id}`);
  }

  async getLogStatistics(): Promise<LogStatistics> {
    return this.request<LogStatistics>('GET', '/api/v1/logs/statistics');
  }

  async getErrorLogs(params: ErrorLogParams = { page: 1, page_size: 20 }): Promise<PaginationResponse<RequestLog>> {
    const query = new URLSearchParams(params as any).toString();
    return this.request<PaginationResponse<RequestLog>>('GET', `/api/v1/logs/errors?${query}`);
  }

  async getRecentLogs(limit = 10): Promise<RequestLog[]> {
    return this.request<RequestLog[]>('GET', `/api/v1/logs/recent?limit=${limit}`);
  }
}

// 使用示例
const api = new QwenSolveAPI();

// 获取题目列表
try {
  const questions = await api.getQuestions({
    page: 1,
    page_size: 20,
    quest_type: '单选题'
  });
  console.log('题目列表:', questions);
} catch (error) {
  console.error('获取失败:', error.message);
}

// 创建题目
try {
  const newQuestion = await api.createQuestion({
    quest_type: '单选题',
    quest_content: '这是一个测试题目',
    quest_options: {
      A: '选项A',
      B: '选项B',
      C: '选项C',
      D: '选项D'
    },
    answer: {
      A: '选项A'
    },
    analysis: '这是答案解析'
  });
  console.log('创建成功:', newQuestion);
} catch (error) {
  console.error('创建失败:', error.message);
}

// 获取日志统计
try {
  const stats = await api.getLogStatistics();
  console.log('统计数据:', stats);
} catch (error) {
  console.error('获取统计失败:', error.message);
}
```

### React Hook 示例

```javascript
import { useState, useEffect } from 'react';

// 自定义Hook：题目管理
export function useQuestions() {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const api = new QwenSolveAPI();

  const fetchQuestions = async (params = {}) => {
    setLoading(true);
    setError(null);
    try {
      const data = await api.getQuestions(params);
      setQuestions(data.list);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createQuestion = async (questionData) => {
    try {
      const newQuestion = await api.createQuestion(questionData);
      setQuestions(prev => [newQuestion, ...prev]);
      return newQuestion;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateQuestion = async (id, questionData) => {
    try {
      const updatedQuestion = await api.updateQuestion(id, questionData);
      setQuestions(prev => 
        prev.map(q => q.id === id ? updatedQuestion : q)
      );
      return updatedQuestion;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteQuestion = async (id) => {
    try {
      await api.deleteQuestion(id);
      setQuestions(prev => prev.filter(q => q.id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    questions,
    loading,
    error,
    fetchQuestions,
    createQuestion,
    updateQuestion,
    deleteQuestion
  };
}

// 自定义Hook：日志管理
export function useLogs() {
  const [logs, setLogs] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const api = new QwenSolveAPI();

  const fetchLogs = async (params = {}) => {
    setLoading(true);
    setError(null);
    try {
      const data = await api.getLogs(params);
      setLogs(data.list);
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const stats = await api.getLogStatistics();
      setStatistics(stats);
      return stats;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteLog = async (id) => {
    try {
      await api.deleteLog(id);
      setLogs(prev => prev.filter(log => log.id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    logs,
    statistics,
    loading,
    error,
    fetchLogs,
    fetchStatistics,
    deleteLog
  };
}
```

### Vue.js 组合式API示例

```javascript
// composables/useQwenAPI.js
import { ref, reactive } from 'vue';

export function useQwenAPI() {
  const loading = ref(false);
  const error = ref(null);

  const api = new QwenSolveAPI();

  const handleRequest = async (requestFn) => {
    loading.value = true;
    error.value = null;
    try {
      const result = await requestFn();
      return result;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    api,
    handleRequest
  };
}

// 题目管理组件
export function useQuestionManager() {
  const { loading, error, api, handleRequest } = useQwenAPI();
  const questions = ref([]);
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  const fetchQuestions = async (params = {}) => {
    return handleRequest(async () => {
      const data = await api.getQuestions({
        page: pagination.page,
        page_size: pagination.pageSize,
        ...params
      });
      questions.value = data.list;
      pagination.total = data.total;
      pagination.totalPages = data.total_pages;
      return data;
    });
  };

  const createQuestion = async (questionData) => {
    return handleRequest(async () => {
      const newQuestion = await api.createQuestion(questionData);
      questions.value.unshift(newQuestion);
      pagination.total++;
      return newQuestion;
    });
  };

  const updateQuestion = async (id, questionData) => {
    return handleRequest(async () => {
      const updatedQuestion = await api.updateQuestion(id, questionData);
      const index = questions.value.findIndex(q => q.id === id);
      if (index !== -1) {
        questions.value[index] = updatedQuestion;
      }
      return updatedQuestion;
    });
  };

  const deleteQuestion = async (id) => {
    return handleRequest(async () => {
      await api.deleteQuestion(id);
      questions.value = questions.value.filter(q => q.id !== id);
      pagination.total--;
    });
  };

  return {
    questions,
    pagination,
    loading,
    error,
    fetchQuestions,
    createQuestion,
    updateQuestion,
    deleteQuestion
  };
}
```

### 完整的Vue组件示例

```vue
<template>
  <div class="question-manager">
    <!-- 搜索和筛选 -->
    <div class="filters">
      <el-form :model="filters" inline>
        <el-form-item label="题目类型">
          <el-select v-model="filters.quest_type" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option label="单选题" value="单选题"></el-option>
            <el-option label="多选题" value="多选题"></el-option>
            <el-option label="判断题" value="判断题"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="验证状态">
          <el-select v-model="filters.is_verified" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option label="已验证" :value="true"></el-option>
            <el-option label="未验证" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input v-model="filters.keyword" placeholder="搜索题目内容"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 题目列表 -->
    <el-table
      :data="questions"
      :loading="loading"
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="quest_type" label="类型" width="100"></el-table-column>
      <el-table-column prop="quest_content" label="题目内容" show-overflow-tooltip></el-table-column>
      <el-table-column prop="is_verified" label="验证状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_verified ? 'success' : 'warning'">
            {{ row.is_verified ? '已验证' : '未验证' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleView(row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useQuestionManager } from '@/composables/useQwenAPI';

const {
  questions,
  pagination,
  loading,
  error,
  fetchQuestions,
  deleteQuestion
} = useQuestionManager();

const filters = reactive({
  quest_type: '',
  is_verified: '',
  keyword: ''
});

// 搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchQuestions(filters);
};

// 重置
const handleReset = () => {
  Object.assign(filters, {
    quest_type: '',
    is_verified: '',
    keyword: ''
  });
  handleSearch();
};

// 分页
const handleSizeChange = (size) => {
  pagination.pageSize = size;
  fetchQuestions(filters);
};

const handleCurrentChange = (page) => {
  pagination.page = page;
  fetchQuestions(filters);
};

// 操作
const handleView = (row) => {
  // 查看题目详情
  console.log('查看题目:', row);
};

const handleEdit = (row) => {
  // 编辑题目
  console.log('编辑题目:', row);
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个题目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await deleteQuestion(row.id);
    ElMessage.success('删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message);
    }
  }
};

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN');
};

// 初始化
onMounted(() => {
  fetchQuestions();
});
</script>
```

## 📝 注意事项

1. **分页参数**: 所有列表接口都支持分页，页码从1开始
2. **时间格式**: 使用ISO 8601格式 (`2025-06-14T10:00:00Z`)
3. **题目类型**: 只支持 `单选题`、`多选题`、`判断题` 三种类型
4. **选项格式**:
   - 单选题/多选题: A、B、C、D 四个选项
   - 判断题: Y、N 或 A、B 两个选项
5. **答案格式**: 使用对象格式，键为选项字母，值为选项内容
6. **图片URL**: 必须是有效的HTTP/HTTPS链接
7. **错误处理**: 建议对所有API调用进行try-catch处理
8. **UI框架**: 示例使用Element Plus，可根据项目需要调整
9. **状态管理**: 建议使用Pinia或Vuex进行全局状态管理
10. **类型安全**: 推荐使用TypeScript提高代码质量

## 📋 常见问题 FAQ

### Q1: 如何处理网络错误？
```typescript
try {
  const questions = await api.getQuestions();
} catch (error) {
  if (error.message.includes('fetch')) {
    // 网络连接错误
    console.error('网络连接失败，请检查网络设置');
  } else {
    // API业务错误
    console.error('API调用失败:', error.message);
  }
}
```

### Q2: 如何实现自动重试？
```typescript
async function requestWithRetry<T>(
  requestFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
  throw new Error('Max retries exceeded');
}

// 使用示例
const questions = await requestWithRetry(() => api.getQuestions());
```

### Q3: 如何实现请求缓存？
```typescript
class CachedQwenAPI extends QwenSolveAPI {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5分钟

  async getQuestions(params: QuestionListParams = { page: 1, page_size: 20 }) {
    const cacheKey = `questions_${JSON.stringify(params)}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const data = await super.getQuestions(params);
    this.cache.set(cacheKey, { data, timestamp: Date.now() });
    return data;
  }
}
```

### Q4: 如何处理大量数据的分页加载？
```typescript
async function loadAllQuestions(api: QwenSolveAPI) {
  const allQuestions: Question[] = [];
  let page = 1;
  const pageSize = 100;

  while (true) {
    const response = await api.getQuestions({ page, page_size: pageSize });
    allQuestions.push(...response.list);

    if (page >= response.total_pages) {
      break;
    }
    page++;
  }

  return allQuestions;
}
```

## 🔗 相关链接

- [API测试工具](http://localhost:8080/health) - 健康检查接口
- [服务状态监控](http://localhost:8080/api/v1/logs/statistics) - 服务统计信息
- [GitHub仓库](https://github.com/your-org/qwen-solve) - 源码仓库
- [技术支持](mailto:<EMAIL>) - 技术支持邮箱

## 📞 技术支持

如果在接入过程中遇到问题，请提供以下信息：

1. **错误信息**: 完整的错误堆栈
2. **请求参数**: 发送的请求参数
3. **环境信息**: 浏览器版本、框架版本等
4. **复现步骤**: 详细的操作步骤

**联系方式**:
- 邮箱: <EMAIL>
- 微信群: 扫描二维码加入技术交流群
- 工单系统: [提交工单](https://support.example.com)

---

**文档版本**: v1.0
**更新时间**: 2025-06-14
**维护团队**: Qwen-Solve 开发团队
**下次更新**: 根据反馈持续优化
