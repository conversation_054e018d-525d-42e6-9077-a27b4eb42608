# 数据库迁移指南 - 移除hash_key唯一性约束

## 📋 概述

本迁移旨在修正题库管理系统的数据库结构，移除`hash_key`字段的唯一性约束，允许相同的缓存键存入数据库，以支持题库管理的业务需求。

## 🎯 迁移目标

- **移除唯一性约束**：删除`qwen_solve_questions`表中`hash_key`字段的`UNIQUE KEY uk_hash_key`约束
- **保留索引性能**：添加普通索引`idx_hash_key`以保持查询性能
- **支持业务需求**：允许管理员手动修改hash_key来处理特殊场景

## 🔧 迁移步骤

### 1. 备份数据库
```bash
# 备份整个数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 或仅备份题库表
mysqldump -u username -p database_name qwen_solve_questions > questions_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 执行迁移脚本
```bash
# 执行迁移脚本
mysql -u username -p database_name < migrations/001_remove_hash_key_unique_constraint.sql
```

### 3. 验证迁移结果
```sql
-- 检查约束是否已移除
SELECT CONSTRAINT_NAME, CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
WHERE TABLE_SCHEMA = 'your_database_name' 
  AND TABLE_NAME = 'qwen_solve_questions'
  AND CONSTRAINT_NAME = 'uk_hash_key';

-- 检查索引是否已添加
SELECT INDEX_NAME, NON_UNIQUE, COLUMN_NAME
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'your_database_name' 
  AND TABLE_NAME = 'qwen_solve_questions'
  AND INDEX_NAME = 'idx_hash_key';
```

## 📊 迁移前后对比

### 迁移前
```sql
CREATE TABLE `qwen_solve_questions` (
  -- ... 其他字段
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_hash_key` (`hash_key`),  -- 唯一性约束
  -- ... 其他索引
);
```

### 迁移后
```sql
CREATE TABLE `qwen_solve_questions` (
  -- ... 其他字段
  PRIMARY KEY (`id`),
  KEY `idx_hash_key` (`hash_key`),        -- 普通索引
  -- ... 其他索引
);
```

## 🚨 注意事项

### 1. 数据一致性
- 迁移前检查是否存在重复的hash_key值
- 如果存在重复值，迁移会成功，这是预期行为

### 2. 应用程序兼容性
- 现有代码已经支持多条记录具有相同hash_key
- `GetAllByHashKey`方法会返回所有匹配的记录
- 不需要修改应用程序代码

### 3. 性能影响
- 移除唯一性约束后，插入性能可能略有提升
- 普通索引仍然保证查询性能
- 对于大量数据的查询，性能基本无变化

## 🔄 回滚方案

如果需要回滚迁移，可以执行以下步骤：

```sql
-- 1. 检查是否存在重复的hash_key
SELECT hash_key, COUNT(*) as count 
FROM qwen_solve_questions 
WHERE deleted_at IS NULL 
GROUP BY hash_key 
HAVING COUNT(*) > 1;

-- 2. 如果没有重复值，可以重新添加唯一性约束
ALTER TABLE qwen_solve_questions DROP INDEX idx_hash_key;
ALTER TABLE qwen_solve_questions ADD UNIQUE KEY uk_hash_key (hash_key);

-- 3. 如果有重复值，需要先处理重复数据
-- （根据业务需求决定如何处理重复记录）
```

## ✅ 验证清单

迁移完成后，请验证以下项目：

- [ ] 唯一性约束`uk_hash_key`已被移除
- [ ] 普通索引`idx_hash_key`已被添加
- [ ] 可以成功插入具有相同hash_key的记录
- [ ] 查询功能正常工作
- [ ] 应用程序启动正常
- [ ] API接口功能正常

## 📞 支持

如果在迁移过程中遇到问题，请：

1. 检查错误日志
2. 确认数据库权限
3. 验证SQL语法
4. 联系开发团队获取支持

---

**迁移版本**: 001  
**创建日期**: 2025-01-14  
**维护者**: 开发团队
