- 将qwen-vl-plus模型返回的原始数据执行以下处理；

1. 从qutext字段中取出（题目类型）/判断题/单选题/多选题  一共三种题目类型；
    - 必须提取正确的题型，正确题型只有上述三种，如果未能正确提取题型 则中止进程返回错误：图片不标准，题目类型未正确识别，请重新拍摄！
    - 将提取到的题型保存到ProcessContext中；
    - 题型命名：quest_type

3. 从qutext字段中取出（题目正文）并保存到ProcessContext中；
    - 使用这个正则对qutext进行清洗re := regexp.MustCompile(`(?s)^[（(]?(单选题|多选题|判断题)[）)]?(\d+)[、.，,：:]?(.*)$`)
    - 清洗后得到干净的题干内容。
    - 将清洗后的题干内容保存到ProcessContext中；
    - 找一个可靠方案来验证清洗后的题干是否干净，如果清洗不干净，则中止进程返回错误：图片不标准，题干未正确识别，请重新拍摄！
    - 题干命名：quest_content

    2. 从options字段中取出所有选项，并保存到ProcessContext中；
    - 单选或多选，必须保证四个选项，如果选项数量不为4个，则中止进程返回错误：图片不标准，请正确拍摄！
    - 判断题，必须保证两个选项，如果选项数量不为2个，则中止进程返回错误：图片不标准，选项未正确识别，请重新拍摄！
    - 选项命名：quest_option