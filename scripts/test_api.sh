#!/bin/bash

# API测试脚本
# 用于测试Qwen Solve API的各个接口

BASE_URL="http://localhost:8080"
TEST_IMAGE_URL="http://img.igmdns.com/img/ca0001.jpg"

echo "=== Qwen Solve API 测试脚本 ==="
echo "基础URL: $BASE_URL"
echo "测试图片: $TEST_IMAGE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
        echo "响应: $(echo "$body" | jq -r '.message // .code // "成功"' 2>/dev/null || echo "成功")"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    echo ""
}

# 检查服务是否运行
echo "检查服务状态..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo -e "${RED}错误: 服务未运行，请先启动服务${NC}"
    echo "启动命令: ./build/qwen-solve-server"
    exit 1
fi

echo -e "${GREEN}✓ 服务正在运行${NC}"
echo ""

# 1. 健康检查
test_api "GET" "/health" "" "健康检查"

# 2. 验证图片
test_api "POST" "/api/v1/solve/validate" "{\"image_url\":\"$TEST_IMAGE_URL\"}" "验证图片URL"

# 3. 图片解题（这个可能会失败，因为需要真实的数据库和Redis连接）
test_api "POST" "/api/v1/solve" "{\"image_url\":\"$TEST_IMAGE_URL\"}" "图片解题"

# 4. 获取解题历史
test_api "GET" "/api/v1/solve/history?page=1&page_size=10" "" "获取解题历史"

# 5. 获取解题统计
test_api "GET" "/api/v1/solve/statistics" "" "获取解题统计"

# 6. 获取请求日志
test_api "GET" "/api/v1/logs?page=1&page_size=10" "" "获取请求日志"

# 7. 获取日志统计
test_api "GET" "/api/v1/logs/statistics" "" "获取日志统计"

# 8. 获取题目列表
test_api "GET" "/api/v1/questions?page=1&page_size=10" "" "获取题目列表"

# 9. 搜索题目
test_api "GET" "/api/v1/questions/search?keyword=测试" "" "搜索题目"

# 10. 测试不存在的接口
test_api "GET" "/api/v1/nonexistent" "" "测试404错误"

echo "=== 测试完成 ==="
echo ""
echo "注意事项："
echo "1. 某些接口可能因为缺少数据库连接而失败"
echo "2. 图片解题接口需要有效的Qwen API密钥"
echo "3. 请确保MySQL和Redis服务正在运行"
echo ""
echo "如需查看详细日志，请检查服务器输出"
