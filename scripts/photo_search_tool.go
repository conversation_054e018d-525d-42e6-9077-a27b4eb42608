package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sync"
	"sync/atomic"
	"time"
)

// APIRequest 请求结构体
type APIRequest struct {
	ImageURL string `json:"image_url"`
}

// APIResponse 响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// TestResult 测试结果
type TestResult struct {
	ImageURL     string
	Success      bool
	ErrorMessage string
	ResponseTime time.Duration
	StatusCode   int
}

// Statistics 统计信息
type Statistics struct {
	Total     int64
	Success   int64
	Failed    int64
	StartTime time.Time
}

const (
	// API配置
	APIBaseURL = "http://localhost:8080"
	APIPath    = "/api/v1/solve"
	
	// 测试配置
	NumWorkers    = 10
	StartImageNum = 1
	EndImageNum   = 1089
	ImageBaseURL  = "http://img.igmdns.com/images/cc%04d.jpg"
	
	// 超时配置
	RequestTimeout = 60 * time.Second
)

var (
	stats     Statistics
	logFile   *os.File
	logMutex  sync.Mutex
)

func main() {
	fmt.Println("=== 拍照搜题API多线程测试工具 ===")
	fmt.Printf("API地址: %s%s\n", APIBaseURL, APIPath)
	fmt.Printf("测试图片: cc%04d.jpg - cc%04d.jpg\n", StartImageNum, EndImageNum)
	fmt.Printf("并发线程: %d\n", NumWorkers)
	fmt.Printf("图片总数: %d\n", EndImageNum-StartImageNum+1)
	fmt.Println("========================================")

	// 初始化日志文件
	if err := initLogFile(); err != nil {
		log.Fatalf("初始化日志文件失败: %v", err)
	}
	defer logFile.Close()

	// 初始化统计信息
	stats.StartTime = time.Now()
	stats.Total = int64(EndImageNum - StartImageNum + 1)

	// 创建任务通道
	taskChan := make(chan int, NumWorkers*2)
	resultChan := make(chan TestResult, NumWorkers*2)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < NumWorkers; i++ {
		wg.Add(1)
		go worker(i+1, taskChan, resultChan, &wg)
	}

	// 启动结果处理协程
	go resultHandler(resultChan)

	// 启动进度显示协程
	go progressDisplay()

	// 发送任务
	go func() {
		for i := StartImageNum; i <= EndImageNum; i++ {
			taskChan <- i
		}
		close(taskChan)
	}()

	// 等待所有工作协程完成
	wg.Wait()
	close(resultChan)

	// 等待一秒确保所有结果都被处理
	time.Sleep(time.Second)

	// 显示最终统计
	showFinalStats()
}

// initLogFile 初始化日志文件
func initLogFile() error {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("photo_search_test_%s.log", timestamp)
	
	var err error
	logFile, err = os.Create(filename)
	if err != nil {
		return err
	}

	// 写入日志头部
	header := fmt.Sprintf("=== 拍照搜题API测试日志 ===\n")
	header += fmt.Sprintf("开始时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	header += fmt.Sprintf("API地址: %s%s\n", APIBaseURL, APIPath)
	header += fmt.Sprintf("测试范围: cc%04d.jpg - cc%04d.jpg\n", StartImageNum, EndImageNum)
	header += fmt.Sprintf("并发数: %d\n", NumWorkers)
	header += fmt.Sprintf("========================================\n\n")
	
	_, err = logFile.WriteString(header)
	return err
}

// worker 工作协程
func worker(workerID int, taskChan <-chan int, resultChan chan<- TestResult, wg *sync.WaitGroup) {
	defer wg.Done()
	
	client := &http.Client{
		Timeout: RequestTimeout,
	}

	for imageNum := range taskChan {
		imageURL := fmt.Sprintf(ImageBaseURL, imageNum)
		result := testSingleImage(client, imageURL, workerID)
		resultChan <- result
	}
}

// testSingleImage 测试单张图片
func testSingleImage(client *http.Client, imageURL string, workerID int) TestResult {
	startTime := time.Now()
	
	// 构造请求
	reqBody := APIRequest{
		ImageURL: imageURL,
	}
	
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return TestResult{
			ImageURL:     imageURL,
			Success:      false,
			ErrorMessage: fmt.Sprintf("JSON序列化失败: %v", err),
			ResponseTime: time.Since(startTime),
			StatusCode:   0,
		}
	}

	// 发送请求
	req, err := http.NewRequest("POST", APIBaseURL+APIPath, bytes.NewBuffer(jsonData))
	if err != nil {
		return TestResult{
			ImageURL:     imageURL,
			Success:      false,
			ErrorMessage: fmt.Sprintf("创建请求失败: %v", err),
			ResponseTime: time.Since(startTime),
			StatusCode:   0,
		}
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return TestResult{
			ImageURL:     imageURL,
			Success:      false,
			ErrorMessage: fmt.Sprintf("请求失败: %v", err),
			ResponseTime: time.Since(startTime),
			StatusCode:   0,
		}
	}
	defer resp.Body.Close()

	responseTime := time.Since(startTime)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return TestResult{
			ImageURL:     imageURL,
			Success:      false,
			ErrorMessage: fmt.Sprintf("读取响应失败: %v", err),
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
		}
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return TestResult{
			ImageURL:     imageURL,
			Success:      false,
			ErrorMessage: fmt.Sprintf("解析响应失败: %v, 响应内容: %s", err, string(body)),
			ResponseTime: responseTime,
			StatusCode:   resp.StatusCode,
		}
	}

	// 判断是否成功
	success := resp.StatusCode == 200 && apiResp.Code == 0
	errorMsg := ""
	if !success {
		if resp.StatusCode != 200 {
			errorMsg = fmt.Sprintf("HTTP状态码: %d, 响应: %s", resp.StatusCode, string(body))
		} else {
			errorMsg = fmt.Sprintf("API错误码: %d, 错误信息: %s", apiResp.Code, apiResp.Message)
		}
	}

	return TestResult{
		ImageURL:     imageURL,
		Success:      success,
		ErrorMessage: errorMsg,
		ResponseTime: responseTime,
		StatusCode:   resp.StatusCode,
	}
}

// resultHandler 结果处理协程
func resultHandler(resultChan <-chan TestResult) {
	for result := range resultChan {
		if result.Success {
			atomic.AddInt64(&stats.Success, 1)
		} else {
			atomic.AddInt64(&stats.Failed, 1)
			// 记录错误到日志文件
			logError(result)
		}
	}
}

// logError 记录错误到日志文件
func logError(result TestResult) {
	logMutex.Lock()
	defer logMutex.Unlock()

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logEntry := fmt.Sprintf("[%s] 错误 - 图片: %s, 状态码: %d, 响应时间: %v, 错误信息: %s\n",
		timestamp, result.ImageURL, result.StatusCode, result.ResponseTime, result.ErrorMessage)
	
	logFile.WriteString(logEntry)
	logFile.Sync() // 立即刷新到磁盘
}

// progressDisplay 进度显示协程
func progressDisplay() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			showProgress()
		}
	}
}

// showProgress 显示进度
func showProgress() {
	success := atomic.LoadInt64(&stats.Success)
	failed := atomic.LoadInt64(&stats.Failed)
	completed := success + failed
	
	if completed == 0 {
		return
	}

	elapsed := time.Since(stats.StartTime)
	rate := float64(completed) / elapsed.Seconds()
	remaining := stats.Total - completed
	eta := time.Duration(float64(remaining)/rate) * time.Second

	fmt.Printf("\r进度: %d/%d (%.1f%%) | 成功: %d | 失败: %d | 速度: %.1f req/s | 预计剩余: %v",
		completed, stats.Total, float64(completed)/float64(stats.Total)*100,
		success, failed, rate, eta)
}

// showFinalStats 显示最终统计
func showFinalStats() {
	fmt.Println("\n\n=== 测试完成 ===")
	
	success := atomic.LoadInt64(&stats.Success)
	failed := atomic.LoadInt64(&stats.Failed)
	total := success + failed
	elapsed := time.Since(stats.StartTime)
	
	fmt.Printf("总计: %d\n", total)
	fmt.Printf("成功: %d (%.1f%%)\n", success, float64(success)/float64(total)*100)
	fmt.Printf("失败: %d (%.1f%%)\n", failed, float64(failed)/float64(total)*100)
	fmt.Printf("总耗时: %v\n", elapsed)
	fmt.Printf("平均速度: %.2f req/s\n", float64(total)/elapsed.Seconds())
	
	if failed > 0 {
		fmt.Printf("\n错误详情已记录到日志文件: %s\n", logFile.Name())
	}
	
	fmt.Println("==================")
}
