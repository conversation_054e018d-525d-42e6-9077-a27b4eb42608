# 拍照搜题API多线程测试工具

## 功能说明

这是一个用Go语言编写的多线程测试工具，用于测试拍照搜题API的性能和稳定性。

### 主要特性

- **多线程并发**: 使用10个goroutine并发测试，提高测试效率
- **批量测试**: 自动测试cc0001.jpg到cc1089.jpg共1089张图片
- **错误记录**: 自动将错误信息记录到日志文件
- **实时进度**: 每5秒显示一次测试进度和统计信息
- **详细统计**: 显示成功率、失败率、响应时间等统计信息

## 文件说明

- `photo_search_tool.go` - 主测试程序
- `run_photo_test.sh` - 运行脚本（Linux/macOS）
- `README_photo_test.md` - 使用说明文档

## 使用方法

### 方法1: 使用运行脚本（推荐）

```bash
# 使用默认API地址 (http://solve.igmdns.com)
./scripts/run_photo_test.sh

# 使用自定义API地址
./scripts/run_photo_test.sh http://your-api-server:8080
```

### 方法2: 直接运行Go程序

```bash
cd scripts
go run photo_search_tool.go
```

### 方法3: 编译后运行

```bash
cd scripts
go build -o photo_test photo_search_tool.go
./photo_test
```

## 配置参数

可以在`photo_search_tool.go`文件中修改以下参数：

```go
const (
    // API配置
    APIBaseURL = "http://solve.igmdns.com"  // API服务地址
    APIPath    = "/api/v1/solve"            // API路径
    
    // 测试配置
    NumWorkers    = 10                    // 并发线程数
    StartImageNum = 1                     // 起始图片编号
    EndImageNum   = 1089                  // 结束图片编号
    ImageBaseURL  = "http://img.igmdns.com/images/cc%04d.jpg"  // 图片URL模板
    
    // 超时配置
    RequestTimeout = 60 * time.Second     // 请求超时时间
)
```

## 输出说明

### 控制台输出

程序运行时会显示：
- 测试配置信息
- 实时进度（每5秒更新）
- 最终统计结果

示例输出：
```
=== 拍照搜题API多线程测试工具 ===
API地址: http://solve.igmdns.com/api/v1/solve
测试图片: cc0001.jpg - cc1089.jpg
并发线程: 10
图片总数: 1089
========================================

进度: 150/1089 (13.8%) | 成功: 145 | 失败: 5 | 速度: 2.5 req/s | 预计剩余: 6m15s

=== 测试完成 ===
总计: 1089
成功: 1050 (96.4%)
失败: 39 (3.6%)
总耗时: 7m23s
平均速度: 2.46 req/s

错误详情已记录到日志文件: photo_search_test_20240614_143052.log
==================
```

### 日志文件

错误信息会自动记录到以时间戳命名的日志文件中，格式如下：

```
=== 拍照搜题API测试日志 ===
开始时间: 2024-06-14 14:30:52
API地址: http://solve.igmdns.com/api/v1/solve
测试范围: cc0001.jpg - cc1089.jpg
并发数: 10
========================================

[2024-06-14 14:31:15] 错误 - 图片: http://img.igmdns.com/images/cc0025.jpg, 状态码: 500, 响应时间: 2.5s, 错误信息: API错误码: 5000, 错误信息: 服务器内部错误
[2024-06-14 14:31:18] 错误 - 图片: http://img.igmdns.com/images/cc0033.jpg, 状态码: 0, 响应时间: 60s, 错误信息: 请求失败: context deadline exceeded
```

## 测试场景

该工具主要用于以下测试场景：

1. **性能测试**: 测试API在高并发情况下的响应性能
2. **稳定性测试**: 长时间运行测试API的稳定性
3. **错误率统计**: 统计API的错误率和错误类型
4. **负载测试**: 模拟真实用户的并发访问

## 注意事项

1. **网络环境**: 确保测试环境能够访问图片URL和API服务
2. **服务状态**: 确保API服务正在运行且可访问
3. **资源消耗**: 10个并发线程会产生一定的网络和CPU负载
4. **超时设置**: 默认60秒超时，可根据实际情况调整
5. **图片资源**: 测试图片需要能够正常访问

## 故障排除

### 常见错误及解决方案

1. **连接被拒绝**
   ```
   错误信息: 请求失败: dial tcp 127.0.0.1:8080: connect: connection refused
   解决方案: 检查API服务是否启动，端口是否正确
   ```

2. **请求超时**
   ```
   错误信息: 请求失败: context deadline exceeded
   解决方案: 增加RequestTimeout值或检查网络连接
   ```

3. **图片无法访问**
   ```
   错误信息: API错误码: 4001, 错误信息: 图片无法访问或格式不支持
   解决方案: 检查图片URL是否可访问
   ```

4. **Go环境问题**
   ```
   错误信息: go: command not found
   解决方案: 安装Go语言环境
   ```

## 扩展功能

可以根据需要扩展以下功能：

1. **自定义图片列表**: 修改图片URL生成逻辑
2. **结果导出**: 将测试结果导出为CSV或JSON格式
3. **性能监控**: 添加响应时间分布统计
4. **重试机制**: 为失败的请求添加重试逻辑
5. **配置文件**: 使用配置文件管理测试参数

## 技术实现

- **语言**: Go 1.19+
- **并发模型**: Goroutine + Channel
- **HTTP客户端**: 标准库net/http
- **JSON处理**: 标准库encoding/json
- **日志记录**: 文件写入 + 互斥锁保护
