#!/bin/bash

# Qwen Solve API 生产环境部署脚本
# 使用方法: ./scripts/deploy.sh [server_ip] [ssh_user]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 <server_ip> <ssh_user>"
    log_error "示例: $0 ************* root"
    exit 1
fi

SERVER_IP=$1
SSH_USER=$2
APP_NAME="qwen-solve"
APP_DIR="/opt/${APP_NAME}"
BINARY_NAME="qwen-solve-server-linux-amd64"

log_info "开始部署 ${APP_NAME} 到服务器 ${SERVER_IP}"

# 检查本地文件
log_step "检查本地文件..."
if [ ! -f "build/${BINARY_NAME}" ]; then
    log_error "可执行文件不存在: build/${BINARY_NAME}"
    log_error "请先运行: make build-linux"
    exit 1
fi

if [ ! -f ".env.production.template" ]; then
    log_error "环境变量模板文件不存在: .env.production.template"
    exit 1
fi

log_info "本地文件检查完成"

# 创建远程目录
log_step "创建远程目录结构..."
ssh ${SSH_USER}@${SERVER_IP} "
    sudo mkdir -p ${APP_DIR}
    sudo mkdir -p ${APP_DIR}/logs
    sudo mkdir -p /var/log/${APP_NAME}
    
    # 创建应用用户（如果不存在）
    if ! id ${APP_NAME} &>/dev/null; then
        sudo useradd -r -s /bin/false ${APP_NAME}
        echo '用户 ${APP_NAME} 创建成功'
    else
        echo '用户 ${APP_NAME} 已存在'
    fi
    
    sudo chown -R ${APP_NAME}:${APP_NAME} ${APP_DIR}
    sudo chown -R ${APP_NAME}:${APP_NAME} /var/log/${APP_NAME}
"

log_info "远程目录创建完成"

# 上传可执行文件
log_step "上传可执行文件..."
scp build/${BINARY_NAME} ${SSH_USER}@${SERVER_IP}:${APP_DIR}/

# 设置文件权限
ssh ${SSH_USER}@${SERVER_IP} "
    sudo chmod +x ${APP_DIR}/${BINARY_NAME}
    sudo chown ${APP_NAME}:${APP_NAME} ${APP_DIR}/${BINARY_NAME}
"

log_info "可执行文件上传完成"

# 上传环境变量模板
log_step "上传配置文件模板..."
scp .env.production.template ${SSH_USER}@${SERVER_IP}:${APP_DIR}/.env.template

ssh ${SSH_USER}@${SERVER_IP} "
    sudo chown ${APP_NAME}:${APP_NAME} ${APP_DIR}/.env.template
    sudo chmod 600 ${APP_DIR}/.env.template
"

log_info "配置文件模板上传完成"

# 创建systemd服务文件
log_step "创建systemd服务文件..."
ssh ${SSH_USER}@${SERVER_IP} "
sudo tee /etc/systemd/system/${APP_NAME}.service << 'EOF'
[Unit]
Description=Qwen Solve API Server
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=${APP_NAME}
Group=${APP_NAME}
WorkingDirectory=${APP_DIR}
ExecStart=${APP_DIR}/${BINARY_NAME}
EnvironmentFile=${APP_DIR}/.env
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${APP_NAME}

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/${APP_NAME}

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
"

log_info "systemd服务文件创建完成"

# 重新加载systemd
log_step "重新加载systemd配置..."
ssh ${SSH_USER}@${SERVER_IP} "
    sudo systemctl daemon-reload
    sudo systemctl enable ${APP_NAME}
"

log_info "systemd配置完成"

# 部署完成提示
log_info "=========================================="
log_info "🎉 部署完成！"
log_info "=========================================="
log_warn "⚠️  接下来需要手动完成以下步骤："
echo ""
log_step "1. 配置环境变量："
echo "   ssh ${SSH_USER}@${SERVER_IP}"
echo "   sudo cp ${APP_DIR}/.env.template ${APP_DIR}/.env"
echo "   sudo nano ${APP_DIR}/.env  # 编辑配置文件"
echo "   sudo chown ${APP_NAME}:${APP_NAME} ${APP_DIR}/.env"
echo "   sudo chmod 600 ${APP_DIR}/.env"
echo ""
log_step "2. 启动服务："
echo "   sudo systemctl start ${APP_NAME}"
echo "   sudo systemctl status ${APP_NAME}"
echo ""
log_step "3. 查看日志："
echo "   sudo journalctl -u ${APP_NAME} -f"
echo ""
log_step "4. 健康检查："
echo "   curl http://${SERVER_IP}:8080/health"
echo ""
log_info "详细部署文档请参考: docs/production-deployment.md"
log_info "=========================================="
