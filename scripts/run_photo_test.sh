#!/bin/bash

# 拍照搜题API测试脚本运行器
# 使用方法: ./run_photo_test.sh [API_BASE_URL]

set -e

# 默认API地址
DEFAULT_API_URL="http://solve.igmdns.com"
API_URL="${1:-$DEFAULT_API_URL}"

echo "=== 拍照搜题API测试工具 ==="
echo "API地址: $API_URL"
echo "=========================="

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 进入脚本目录
cd "$(dirname "$0")"

# 如果API地址不是默认值，需要修改源码中的API地址
if [ "$API_URL" != "$DEFAULT_API_URL" ]; then
    echo "修改API地址为: $API_URL"
    sed -i.bak "s|APIBaseURL = \".*\"|APIBaseURL = \"$API_URL\"|g" photo_search_tool.go
fi

# 初始化Go模块（如果需要）
if [ ! -f "go.mod" ]; then
    echo "初始化Go模块..."
    go mod init photo_search_tool
fi

# 运行测试
echo "开始运行测试..."
go run photo_search_tool.go

# 恢复原始文件（如果有修改）
if [ -f "photo_search_tool.go.bak" ]; then
    mv photo_search_tool.go.bak photo_search_tool.go
fi

echo "测试完成！"
